GlobalPlanner:
  controller_frequency: 10
  allow_unknown: false #true
  planner_frequency: 10.0
  default_tolerance: 1.5 # If goal in obstacle, plan to the closest point in radius default_tolerance, default 0.0
  visualize_potential: false 
  use_dijkstra: false #If true, use <PERSON><PERSON><PERSON>'s algorithm. Otherwise, A*.
  use_quadratic: true
  use_grid_path: false
  old_navfn_behavior: false # Exactly mirror behavior of navfn, use defaults for other boolean parameters, default false