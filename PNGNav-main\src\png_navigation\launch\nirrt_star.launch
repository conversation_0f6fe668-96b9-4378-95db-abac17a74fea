<?xml version="1.0"?>
<launch>
	<arg name="map" default="map_gazebo"/>
    <node pkg="png_navigation" name="nirrt_star_node" type="nirrt_star_node.py" output="screen" />
    <node pkg="png_navigation" name="nirrt_star_neural_wrapper_node" type="nirrt_star_neural_wrapper_node.py" output="screen" />
    <node pkg="png_navigation" name="local_planner_clock" type="local_planner_clock.py" output="screen" />
    <node pkg="png_navigation" name="local_planner_node" type="local_planner_node.py" output="screen" />
    <node pkg="png_navigation" name="global_planner_node" type="global_planner_node.py" args="--use_neural_wrapper --map $(arg map)" output="screen" />
</launch>