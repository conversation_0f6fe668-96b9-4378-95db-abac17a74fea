<launch>

    <arg name="world_name" default="indoor_1"/> <!--office, maze, indoor_1, indoor_2-->
    <arg name="vehicleHeight" default="0.75"/>
    <arg name="cameraOffsetZ" default="0"/>
    <arg name="vehicleX" default="0"/>
    <arg name="vehicleY" default="0"/>
    <arg name="terrainZ" default="0"/>
    <arg name="vehicleYaw" default="0"/>
    <arg name="gazebo_gui" default="true"/>
  
    <include file="$(find local_planner)/launch/local_planner.launch" >
      <arg name="cameraOffsetZ" value="$(arg cameraOffsetZ)"/>
      <arg name="goalX" value="$(arg vehicleX)"/>
      <arg name="goalY" value="$(arg vehicleY)"/>
    </include>
  
    <include file="$(find terrain_analysis)/launch/terrain_analysis.launch" />
  
    <include file="$(find HPHS)/launch/cmu_vehicle_simulator.launch" >
      <arg name="world_name" value="$(arg world_name)"/>
      <arg name="vehicleHeight" value="$(arg vehicleHeight)"/>
      <arg name="cameraOffsetZ" value="$(arg cameraOffsetZ)"/>
      <arg name="vehicleX" value="$(arg vehicleX)"/>
      <arg name="vehicleY" value="$(arg vehicleY)"/>
      <arg name="terrainZ" value="$(arg terrainZ)"/>
      <arg name="vehicleYaw" value="$(arg vehicleYaw)"/>
      <arg name="gui" value="$(arg gazebo_gui)"/>
    </include>
  
    <include file="$(find sensor_scan_generation)/launch/sensor_scan_generation.launch" />
  
    <node launch-prefix="nice" pkg="rviz" type="rviz" name="rvizGA" args="-d $(find HPHS)/rviz/visualization.rviz" respawn="true"/>
  
  </launch>
  