# HPHS: 基于混合边界点采样与分层规划的未知环境探索

## 引言

HPHS是一个用于快速探索未知环境的系统框架。该框架主要由三个模块组成：**混合边界点采样模块**、**子区域分割与选择模块**，以及**边界点选择模块**。这三个模块按照时间顺序依次执行，直到整个环境被完全建模。本代码库是我们方法的**Python实现**。

\<p align="center"\>
\<img src="figure/fig0.png" width="400" height="273"/\>
\<img src="figure/fig2.jpg" width="400" height="225"/\>
\<img src="figure/fig1.png" width="400" height="360"/\>
\</p\>

### 1\. 相关论文

[HPHS: Hierarchical Planning based on Hybrid Frontier Sampling for Unknown Environments Exploration (已被IEEE IROS 2024接收)](https://arxiv.org/pdf/2407.10660)

### 2\. 作者

<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>

### 3\. 引用

如果您在科研中使用了本项目，请引用我们的论文：

```
@article{long2024hphs,
  title={HPHS: Hierarchical Planning based on Hybrid Frontier Sampling for Unknown Environments Exploration},
  author={Long, Shijun and Li, Ying and Wu, Chenming and Xu, Bin and Fan, Wei},
  journal={arXiv preprint arXiv:2407.10660},
  year={2024}
}
```

## 实验

该方法已在仿真和真实环境中进行了测试，详情可观看[实验视频](https://youtu.be/MndZBmBNYSc)。

## 如何使用

注意：本项目已在`Ubuntu 20.04 (ROS Noetic)`下测试，以下依赖项均基于`ROS Noetic`。如果您的ROS版本不是`ROS Noetic`，请将`noetic`替换为您的ROS版本名称。

### 1\. 基础依赖

```bash
sudo apt-get install ros-noetic-navigation \
ros-noetic-octomap-* \
ros-noetic-tf2-sensor-msgs
```

```bash
pip3 install pyquaternion opencv-python
```

### 2\. 仿真环境

本项目运行于卡内基梅隆大学机器人研究所提供的自主探索框架下。

```bash
sudo apt update
sudo apt install libusb-dev
```

```bash
git clone https://github.com/HongbiaoZ/autonomous_exploration_development_environment.git
cd autonomous_exploration_development_environment
git checkout noetic
catkin_make
```

### 3\. 安装 HPHS

```bash
cd ${YOUR_WORKSPACE_PATH}/src
git clone https://github.com/bit-lsj/HPHS.git
```

### 4\. 运行 HPHS

(1) 打开一个新终端并启动仿真环境：

```bash
cd autonomous_exploration_development_environment
source ./devel/setup.sh
source ~/${YOUR_WORKSPACE_PATH}/devel/setup.bash
roslaunch HPHS exploration.launch
```

(2) 打开另一个新终端并开始探索：

```bash
cd ${YOUR_WORKSPACE_PATH}/src/HPHS
python3 ./scripts/explorer.py 
```

### 5\. 在不同环境中探索

在launch文件`./launch/cmu_framework.launch`中，您可以切换不同的场景：

````bash
  <arg name="world_name" default="office"/> ```

## 致谢

在本项目研究过程中，我们学习并参考了以下工作：

1. [Autonomous Exploration Development Environment](https://github.com/HongbiaoZ/autonomous_exploration_development_environment.git)
2. [TDLE](https://github.com/SeanZsya/tdle.git)
3. [GDAE](https://github.com/reiniscimurs/GDAE.git)
4. [RRT Exploration](https://github.com/hasauino/rrt_exploration.git)
5. [TARE](https://github.com/caochao39/tare_planner.git)
6. [Efficient Dense Frontier Detection](https://github.com/larics/cartographer_frontier_detection.git)

我们非常感谢这些项目的贡献。
````