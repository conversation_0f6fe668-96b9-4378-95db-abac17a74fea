name: pngenv
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - ca-certificates=2023.05.30=h06a4308_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.3=he6710b0_2
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - ncurses=6.4=h6a678d5_0
  - openssl=1.1.1u=h7f8727e_0
  - pip=23.1.2=py39h06a4308_0
  - python=3.9.0=hdb3f193_2
  - readline=8.2=h5eee18b_0
  - setuptools=67.8.0=py39h06a4308_0
  - sqlite=3.41.2=h5eee18b_0
  - tk=8.6.12=h1ccaba5_0
  - wheel=0.38.4=py39h06a4308_0
  - xz=5.4.2=h5eee18b_0
  - zlib=1.2.13=h5eee18b_0
  - pip:
      - addict==2.4.0
      - ansi2html==1.8.0
      - asttokens==2.2.1
      - attrs==23.1.0
      - backcall==0.2.0
      - catkin-pkg==0.5.2
      - certifi==2023.5.7
      - charset-normalizer==3.1.0
      - click==8.1.3
      - cmake==3.26.4
      - comm==0.1.3
      - configargparse==1.5.5
      - contourpy==1.1.0
      - cycler==0.11.0
      - dash==2.11.1
      - dash-core-components==2.0.0
      - dash-html-components==2.0.0
      - dash-table==5.0.0
      - debugpy==1.6.7
      - decorator==5.1.1
      - distro==1.8.0
      - docutils==0.20.1
      - empy==3.3.4
      - executing==1.2.0
      - fastjsonschema==2.17.1
      - filelock==3.12.2
      - flask==2.2.5
      - fonttools==4.40.0
      - idna==3.4
      - importlib-metadata==6.7.0
      - importlib-resources==5.12.0
      - ipykernel==6.23.3
      - ipython==8.14.0
      - ipywidgets==8.0.6
      - itsdangerous==2.1.2
      - jedi==0.18.2
      - jinja2==3.1.2
      - joblib==1.3.1
      - jsonschema==4.17.3
      - jupyter-client==8.3.0
      - jupyter-core==5.3.1
      - jupyterlab-widgets==3.0.7
      - kiwisolver==1.4.4
      - lit==16.0.6
      - markupsafe==2.1.3
      - matplotlib==3.7.1
      - matplotlib-inline==0.1.6
      - mpmath==1.3.0
      - multipledispatch==1.0.0
      - nbformat==5.7.0
      - nest-asyncio==1.5.6
      - networkx==3.1
      - numpy==1.25.0
      - nvidia-cublas-cu11==**********
      - nvidia-cuda-cupti-cu11==11.7.101
      - nvidia-cuda-nvrtc-cu11==11.7.99
      - nvidia-cuda-runtime-cu11==11.7.99
      - nvidia-cudnn-cu11==********
      - nvidia-cufft-cu11==*********
      - nvidia-curand-cu11==**********
      - nvidia-cusolver-cu11==********
      - nvidia-cusparse-cu11==*********
      - nvidia-nccl-cu11==2.14.3
      - nvidia-nvtx-cu11==11.7.91
      - open3d==0.17.0
      - opencv-python==********
      - packaging==23.1
      - pandas==2.0.3
      - parso==0.8.3
      - pexpect==4.8.0
      - pickleshare==0.7.5
      - pillow==10.0.0
      - platformdirs==3.8.0
      - plotly==5.15.0
      - prompt-toolkit==3.0.38
      - psutil==5.9.5
      - ptyprocess==0.7.0
      - pure-eval==0.2.2
      - pygments==2.15.1
      - pyparsing==3.1.0
      - pyquaternion==0.9.9
      - pyrr==0.10.3
      - pyrsistent==0.19.3
      - python-dateutil==2.8.2
      - pytz==2023.3
      - pyyaml==6.0
      - pyzmq==25.1.0
      - requests==2.31.0
      - retrying==1.3.4
      - rospkg==1.5.0
      - scikit-learn==1.3.0
      - scipy==1.11.1
      - six==1.16.0
      - stack-data==0.6.2
      - sympy==1.12
      - tenacity==8.2.2
      - threadpoolctl==3.1.0
      - torch==2.0.1
      - torchvision==0.15.2
      - tornado==6.3.2
      - tqdm==4.65.0
      - traitlets==5.9.0
      - transforms3d==0.4.1
      - triton==2.0.0
      - typing-extensions==4.7.0
      - tzdata==2023.3
      - urllib3==2.0.3
      - wcwidth==0.2.6
      - werkzeug==2.2.3
      - widgetsnbextension==4.0.7
      - zipp==3.15.0
