<sdf version='1.7'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
      <spot>
        <inner_angle>0</inner_angle>
        <outer_angle>0</outer_angle>
        <falloff>0</falloff>
      </spot>
    </light>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <gravity>0 0 -9.8</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <physics type='ode'>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <wind/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='complex_env3'>
      <pose>-8.09144 20.0281 0 0 -0 0</pose>
      <link name='Wall_1'>
        <collision name='Wall_1_Collision'>
          <geometry>
            <box>
              <size>11 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_1_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>11 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>19.9445 -20.1124 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_10'>
        <collision name='Wall_10_Collision'>
          <geometry>
            <box>
              <size>8 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_10_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>8 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>12.3195 -17.3874 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_11'>
        <collision name='Wall_11_Collision'>
          <geometry>
            <box>
              <size>3.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_11_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>3.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>8.39449 -15.8374 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_12'>
        <collision name='Wall_12_Collision'>
          <geometry>
            <box>
              <size>1 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_12_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>1 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>7.98397 -14.1774 0 0 -0 2.87979</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_13'>
        <collision name='Wall_13_Collision'>
          <geometry>
            <box>
              <size>1.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_13_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>7.57345 -13.3924 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_14'>
        <collision name='Wall_14_Collision'>
          <geometry>
            <box>
              <size>8.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_14_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>11.7485 -12.7174 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_15'>
        <collision name='Wall_15_Collision'>
          <geometry>
            <box>
              <size>2.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_15_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>2.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>15.9235 -11.5424 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_16'>
        <collision name='Wall_16_Collision'>
          <geometry>
            <box>
              <size>9.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_16_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>9.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>11.1235 -10.3674 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_17'>
        <collision name='Wall_17_Collision'>
          <geometry>
            <box>
              <size>5.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_17_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>5.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>6.32345 -12.9174 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_18'>
        <collision name='Wall_18_Collision'>
          <geometry>
            <box>
              <size>5.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_18_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>5.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>3.77345 -15.4674 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_19'>
        <collision name='Wall_19_Collision'>
          <geometry>
            <box>
              <size>5.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_19_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>5.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>1.22345 -12.9174 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_2'>
        <collision name='Wall_2_Collision'>
          <geometry>
            <box>
              <size>3.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_2_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>3.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>18.2695 -25.5374 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_20'>
        <collision name='Wall_20_Collision'>
          <geometry>
            <box>
              <size>7.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_20_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>7.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-2.32655 -10.3674 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_22'>
        <collision name='Wall_22_Collision'>
          <geometry>
            <box>
              <size>4.21482 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_22_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.21482 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-3.96425 -10.9469 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_23'>
        <collision name='Wall_23_Collision'>
          <geometry>
            <box>
              <size>5.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_23_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>5.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-1.93184 -13.6219 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_24'>
        <collision name='Wall_24_Collision'>
          <geometry>
            <box>
              <size>1.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_24_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-1.13184 -16.2969 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_25'>
        <collision name='Wall_25_Collision'>
          <geometry>
            <box>
              <size>6.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_25_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>6.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-0.331841 -19.4719 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_26'>
        <collision name='Wall_26_Collision'>
          <geometry>
            <box>
              <size>7.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_26_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>7.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-3.88184 -22.6469 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_27'>
        <collision name='Wall_27_Collision'>
          <geometry>
            <box>
              <size>6.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_27_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>6.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-7.43184 -19.5969 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_28'>
        <collision name='Wall_28_Collision'>
          <geometry>
            <box>
              <size>2.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_28_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>2.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-6.25684 -16.5469 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_29'>
        <collision name='Wall_29_Collision'>
          <geometry>
            <box>
              <size>4 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_29_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>4 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-5.08184 -14.6219 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_3'>
        <collision name='Wall_3_Collision'>
          <geometry>
            <box>
              <size>2.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_3_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>2.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>16.5945 -24.2374 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_30'>
        <collision name='Wall_30_Collision'>
          <geometry>
            <box>
              <size>3.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_30_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>3.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-6.75684 -12.6969 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_31'>
        <collision name='Wall_31_Collision'>
          <geometry>
            <box>
              <size>2.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_31_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>2.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-8.43184 -11.3969 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_32'>
        <collision name='Wall_32_Collision'>
          <geometry>
            <box>
              <size>7.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_32_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>7.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-12.1068 -10.0969 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_4'>
        <collision name='Wall_4_Collision'>
          <geometry>
            <box>
              <size>1.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_4_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>16.0445 -22.9374 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_41'>
        <collision name='Wall_41_Collision'>
          <geometry>
            <box>
              <size>0.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_41_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-5.99666 -10.6469 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_43'>
        <collision name='Wall_43_Collision'>
          <geometry>
            <box>
              <size>6.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_43_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>6.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>19.9445 -11.3874 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_44'>
        <collision name='Wall_44_Collision'>
          <geometry>
            <box>
              <size>4 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_44_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>4 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>21.8695 -8.08735 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_45'>
        <collision name='Wall_45_Collision'>
          <geometry>
            <box>
              <size>3.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_45_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>3.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>23.7945 -6.41235 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_46'>
        <collision name='Wall_46_Collision'>
          <geometry>
            <box>
              <size>4.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_46_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>23.7945 -2.56235 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_47'>
        <collision name='Wall_47_Collision'>
          <geometry>
            <box>
              <size>12.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_47_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>12.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>17.7445 -0.387354 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_48'>
        <collision name='Wall_48_Collision'>
          <geometry>
            <box>
              <size>5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_48_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>11.6945 -2.81235 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_49'>
        <collision name='Wall_49_Collision'>
          <geometry>
            <box>
              <size>4 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_49_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>4 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>13.6195 -5.23735 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_5'>
        <collision name='Wall_5_Collision'>
          <geometry>
            <box>
              <size>0.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_5_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>15.4945 -22.7624 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_50'>
        <collision name='Wall_50_Collision'>
          <geometry>
            <box>
              <size>1.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_50_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>15.5445 -5.91235 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_51'>
        <collision name='Wall_51_Collision'>
          <geometry>
            <box>
              <size>9.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_51_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>9.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>10.9945 -6.58735 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_52'>
        <collision name='Wall_52_Collision'>
          <geometry>
            <box>
              <size>4.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_52_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>5.88156 -4.48646 0 0 -0 1.8326</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_53'>
        <collision name='Wall_53_Collision'>
          <geometry>
            <box>
              <size>21.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_53_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>21.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-5.35638 -2.38558 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_54'>
        <collision name='Wall_54_Collision'>
          <geometry>
            <box>
              <size>4.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_54_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-16.0314 -0.085575 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_55'>
        <collision name='Wall_55_Collision'>
          <geometry>
            <box>
              <size>3 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_55_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>3 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-14.6064 2.21442 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_56'>
        <collision name='Wall_56_Collision'>
          <geometry>
            <box>
              <size>13.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_56_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>13.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-13.1814 8.88942 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_57'>
        <collision name='Wall_57_Collision'>
          <geometry>
            <box>
              <size>2.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_57_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>2.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-14.4371 15.9009 0 0 -0 2.87979</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_58'>
        <collision name='Wall_58_Collision'>
          <geometry>
            <box>
              <size>3.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_58_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>3.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-15.6928 17.7874 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_59'>
        <collision name='Wall_59_Collision'>
          <geometry>
            <box>
              <size>2 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_59_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>2 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-15.6928 20.2624 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_6'>
        <collision name='Wall_6_Collision'>
          <geometry>
            <box>
              <size>9.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_6_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>9.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>10.6945 -22.5874 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_60'>
        <collision name='Wall_60_Collision'>
          <geometry>
            <box>
              <size>5.25 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_60_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>5.25 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-13.1428 21.1874 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_61'>
        <collision name='Wall_61_Collision'>
          <geometry>
            <box>
              <size>4.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_61_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>4.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-10.5928 23.3624 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_62'>
        <collision name='Wall_62_Collision'>
          <geometry>
            <box>
              <size>10.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_62_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>10.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-15.8928 25.5374 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_63'>
        <collision name='Wall_63_Collision'>
          <geometry>
            <box>
              <size>19 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_63_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>19 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-21.1928 16.1124 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_64'>
        <collision name='Wall_64_Collision'>
          <geometry>
            <box>
              <size>2.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_64_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>2.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-22.4928 6.68735 0 0 -0 3.14159</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_65'>
        <collision name='Wall_65_Collision'>
          <geometry>
            <box>
              <size>16.75 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_65_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>16.75 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-23.7928 -1.61265 0 0 -0 -1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_66'>
        <collision name='Wall_66_Collision'>
          <geometry>
            <box>
              <size>8.16306 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_66_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>8.16306 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>-19.7873 -10.0048 0 0 -0 -0.022998</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_7'>
        <collision name='Wall_7_Collision'>
          <geometry>
            <box>
              <size>5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_7_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>5.89449 -20.1624 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_8'>
        <collision name='Wall_8_Collision'>
          <geometry>
            <box>
              <size>10.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_8_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>10.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>11.0695 -17.7374 0 0 -0 0</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='Wall_9'>
        <collision name='Wall_9_Collision'>
          <geometry>
            <box>
              <size>0.5 0.15 2.5</size>
            </box>
          </geometry>
          <pose>0 0 1.25 0 -0 0</pose>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='Wall_9_Visual'>
          <pose>0 0 1.25 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.5 0.15 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
            <ambient>1 1 1 1</ambient>
          </material>
          <meta>
            <layer>0</layer>
          </meta>
        </visual>
        <pose>16.2445 -17.5624 0 0 -0 1.5708</pose>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <static>1</static>
    </model>
    <model name='unit_box'>
      <pose>8.58499 0.438001 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_0'>
      <pose>4.82147 -2.01685 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder'>
      <pose>4.95477 0.968102 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_sphere'>
      <pose>0.887488 0.392933 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.1</iyy>
            <iyz>0</iyz>
            <izz>0.1</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='cart_front_steer'>
      <link name='chassis'>
        <pose>0 0 0.15 0 -0 0</pose>
        <inertial>
          <pose>0.1 0 0 0 -0 0</pose>
          <mass>10</mass>
          <inertia>
            <ixx>0.216667</ixx>
            <iyy>0.841667</iyy>
            <izz>1.04167</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 0.5 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='wheel_front_left'>
        <pose>0.5 0.298 0.15 -1.5708 -0.087266 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_front_left_steer_spin' type='universal'>
        <parent>chassis</parent>
        <child>wheel_front_left</child>
        <axis>
          <xyz>0 -1 0</xyz>
          <limit>
            <lower>-0.610865</lower>
            <upper>0.610865</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <axis2>
          <xyz>0 0 1</xyz>
          <limit>
            <upper>1e+16</upper>
            <lower>-1e+16</lower>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis2>
      </joint>
      <link name='wheel_front_right'>
        <pose>0.5 -0.298 0.15 -1.5708 -0.087266 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_front_right_steer_spin' type='universal'>
        <parent>chassis</parent>
        <child>wheel_front_right</child>
        <axis>
          <xyz>0 -1 0</xyz>
          <limit>
            <lower>-0.610865</lower>
            <upper>0.610865</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <axis2>
          <xyz>0 0 1</xyz>
          <limit>
            <upper>1e+16</upper>
            <lower>-1e+16</lower>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis2>
      </joint>
      <link name='wheel_rear_left'>
        <pose>-0.5 0.298 0.15 -1.5708 0 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_rear_left_spin' type='revolute'>
        <parent>chassis</parent>
        <child>wheel_rear_left</child>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <link name='wheel_rear_right'>
        <pose>-0.5 -0.298 0.15 -1.5708 0 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_rear_right_spin' type='revolute'>
        <parent>chassis</parent>
        <child>wheel_rear_right</child>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <pose>10.357 1.91694 0 0 -0 0</pose>
    </model>
    <model name='cardboard_box'>
      <pose>9.01137 2.62025 0.15 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>2</mass>
          <inertia>
            <ixx>0.0416667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0566667</iyy>
            <iyz>0</iyz>
            <izz>0.0683333</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.5 0.4 0.3</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>1</mu>
                <mu2>1</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode>
                <kp>1e+07</kp>
                <kd>1</kd>
                <min_depth>0.001</min_depth>
                <max_vel>0.1</max_vel>
              </ode>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <pose>0 0 -0.15 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://cardboard_box/meshes/cardboard_box.dae</uri>
              <scale>1.25932 1.00745 0.755591</scale>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='brick_box_3x1x3'>
      <static>1</static>
      <link name='chassis'>
        <pose>0 0 1.5 0 -0 0</pose>
        <collision name='collision'>
          <geometry>
            <box>
              <size>3 1 3</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://brick_box_3x1x3/meshes/simple_box.dae</uri>
              <scale>3 1 3</scale>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://brick_box_3x1x3/materials/scripts</uri>
              <uri>model://brick_box_3x1x3/materials/textures</uri>
              <name>BrickBox/Diffuse</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>3.79801 5.0857 0 0 -0 0</pose>
    </model>
    <model name='cube_20k'>
      <link name='link'>
        <pose>0 0 0.5 0 -0 0</pose>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://cube_20k/meshes/cube_20k.stl</uri>
              <scale>0.5 0.5 0.5</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://cube_20k/meshes/cube_20k.stl</uri>
              <scale>0.5 0.5 0.5</scale>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <inertial>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>9.41568 5.14961 0 0 -0 0</pose>
    </model>
    <model name='double_pendulum_with_base_2'>
      <link name='base'>
        <inertial>
          <mass>100</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='vis_plate_on_ground'>
          <pose>0 0 0.01 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.8</radius>
              <length>0.02</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <visual name='vis_pole'>
          <pose>-0.275 0 1.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.2 0.2 2.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <collision name='col_plate_on_ground'>
          <pose>0 0 0.01 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.8</radius>
              <length>0.02</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='col_pole'>
          <pose>-0.275 0 1.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.2 0.2 2.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='upper_link'>
        <pose>0 0 2.1 -1.5708 0 0</pose>
        <self_collide>0</self_collide>
        <inertial>
          <pose>0 0 0.5 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <visual name='vis_upper_joint'>
          <pose>-0.05 0 0 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.3</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <visual name='vis_lower_joint'>
          <pose>0 0 1 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.2</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <visual name='vis_cylinder'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.9</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <collision name='col_upper_joint'>
          <pose>-0.05 0 0 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.3</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='col_lower_joint'>
          <pose>0 0 1 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.2</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='col_cylinder'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.9</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='lower_link'>
        <pose>0.25 1 2.1 -2 0 0</pose>
        <self_collide>0</self_collide>
        <inertial>
          <pose>0 0 0.5 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <visual name='vis_lower_joint'>
          <pose>0 0 0 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.08</radius>
              <length>0.3</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <visual name='vis_cylinder'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.9</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <collision name='col_lower_joint'>
          <pose>0 0 0 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.08</radius>
              <length>0.3</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='col_cylinder'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.9</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='upper_joint' type='revolute'>
        <parent>base</parent>
        <child>upper_link</child>
        <axis>
          <xyz>1 0 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <joint name='lower_joint' type='revolute'>
        <parent>upper_link</parent>
        <child>lower_link</child>
        <axis>
          <xyz>1 0 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <pose>-3.83841 15.2867 0 0 -0 0</pose>
    </model>
    <model name='double_pendulum_with_base_3'>
      <link name='base'>
        <inertial>
          <mass>100</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
        </inertial>
        <visual name='vis_plate_on_ground'>
          <pose>0 0 0.01 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.8</radius>
              <length>0.02</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <visual name='vis_pole'>
          <pose>-0.275 0 1.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.2 0.2 2.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <collision name='col_plate_on_ground'>
          <pose>0 0 0.01 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.8</radius>
              <length>0.02</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='col_pole'>
          <pose>-0.275 0 1.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.2 0.2 2.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='upper_link'>
        <pose>0 0 2.1 -1.5708 0 0</pose>
        <self_collide>0</self_collide>
        <inertial>
          <pose>0 0 0.5 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <visual name='vis_upper_joint'>
          <pose>-0.05 0 0 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.3</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <visual name='vis_lower_joint'>
          <pose>0 0 1 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.2</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <visual name='vis_cylinder'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.9</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <collision name='col_upper_joint'>
          <pose>-0.05 0 0 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.3</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='col_lower_joint'>
          <pose>0 0 1 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.2</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='col_cylinder'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.9</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='lower_link'>
        <pose>0.25 1 2.1 -2 0 0</pose>
        <self_collide>0</self_collide>
        <inertial>
          <pose>0 0 0.5 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <visual name='vis_lower_joint'>
          <pose>0 0 0 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.08</radius>
              <length>0.3</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <visual name='vis_cylinder'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.9</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <collision name='col_lower_joint'>
          <pose>0 0 0 3.14159 1.57079 3.14159</pose>
          <geometry>
            <cylinder>
              <radius>0.08</radius>
              <length>0.3</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='col_cylinder'>
          <pose>0 0 0.5 0 -0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.1</radius>
              <length>0.9</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='upper_joint' type='revolute'>
        <parent>base</parent>
        <child>upper_link</child>
        <axis>
          <xyz>1 0 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <joint name='lower_joint' type='revolute'>
        <parent>upper_link</parent>
        <child>lower_link</child>
        <axis>
          <xyz>1 0 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <pose>-3.41453 10.7981 0 0 -0 0</pose>
    </model>
    <state world_name='default'>
      <sim_time>2144 782000000</sim_time>
      <real_time>33 609756911</real_time>
      <wall_time>1720073438 515903963</wall_time>
      <iterations>28792</iterations>
      <model name='Dumpster'>
        <pose>-17.827 11.754 0.001373 0 -5e-06 0.493351</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-17.827 11.754 0.001373 0 -5e-06 0.493351</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.006 -0.003225 -4.85257 -0.800339 1.21403 -3.14159</acceleration>
          <wrench>-0.006 -0.003225 -4.85257 0 -0 0</wrench>
        </link>
      </model>
      <model name='Dumpster_0'>
        <pose>-29.1923 12.075 0.001375 2e-06 -0 0.931848</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-29.1923 12.075 0.001375 2e-06 -0 0.931848</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.000309 -0.006635 -5.79969 -1.46841 -0.223717 1e-06</acceleration>
          <wrench>-0.000309 -0.006635 -5.79969 0 -0 0</wrench>
        </link>
      </model>
      <model name='brick_box_3x1x3'>
        <pose>3.79801 5.0857 0 0 0 -0.721177</pose>
        <scale>1 1 1</scale>
        <link name='chassis'>
          <pose>3.79801 5.0857 1.5 0 0 -0.721177</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='camera'>
        <pose>0 0 0.749902 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='camera'>
          <pose>0 0 0.749902 0 -0 0</pose>
          <velocity>0 0 -0.0392 0 -0 0</velocity>
          <acceleration>0 0 -9.8 0 -0 0</acceleration>
          <wrench>0 0 -9.8e-05 0 -0 0</wrench>
        </link>
      </model>
      <model name='cardboard_box'>
        <pose>9.01137 2.62025 0.149 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>9.01137 2.62025 0.149 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 1.9e-05 -0 -0.000128 2e-06 0</acceleration>
          <wrench>1e-06 3.8e-05 -1e-06 0 -0 0</wrench>
        </link>
      </model>
      <model name='cart_front_steer'>
        <pose>11.4248 1.37036 -2e-06 -1.6e-05 -1e-06 0.198064</pose>
        <scale>1 1 1</scale>
        <link name='chassis'>
          <pose>11.4248 1.37036 0.149998 -1.6e-05 -1e-06 0.198064</pose>
          <velocity>-0.000369 -0.000207 -0.003955 -0.014459 -0.002637 -0.000183</velocity>
          <acceleration>-0.008215 0.052683 -9.79364 0.182636 0.075215 -0.008538</acceleration>
          <wrench>-0.082146 0.526835 -97.9364 0 -0 0</wrench>
        </link>
        <link name='wheel_front_left'>
          <pose>11.8564 1.76092 0.149993 1.57084 0.459677 -2.9436</pose>
          <velocity>-0.000326 -7.2e-05 -0.008391 -0.005971 0.00727 -0.039323</velocity>
          <acceleration>0.016955 -0.167639 -9.48992 3.04003 -0.650678 -0.528542</acceleration>
          <wrench>0.008478 -0.083819 -4.74496 0 -0 0</wrench>
        </link>
        <link name='wheel_front_right'>
          <pose>11.9737 1.17657 0.150003 1.57048 -1.3186 -2.94391</pose>
          <velocity>-0.000349 -0.000756 0.000168 -0.021834 -0.008483 -0.017029</velocity>
          <acceleration>0.099464 -0.440556 -10.1623 1.13154 1.48349 2.37116</acceleration>
          <wrench>0.049732 -0.220278 -5.08116 0 -0 0</wrench>
        </link>
        <link name='wheel_rear_left'>
          <pose>10.876 1.56415 0.149993 -1.57084 0.246012 0.198054</pose>
          <velocity>-0.000374 0.000146 -0.008403 -0.007827 -0.003519 -0.000343</velocity>
          <acceleration>0.013654 -0.208214 -9.70304 0.9074 -1.07889 0.333786</acceleration>
          <wrench>0.006827 -0.104107 -4.85152 0 -0 0</wrench>
        </link>
        <link name='wheel_rear_right'>
          <pose>10.9932 0.979803 0.150001 -1.57078 0.306311 0.198069</pose>
          <velocity>-0.000395 -0.000455 0.000702 -0.022132 -0.0076 -0.000264</velocity>
          <acceleration>0.034218 -0.237261 -9.97194 1.55157 1.55054 -0.235425</acceleration>
          <wrench>0.017109 -0.118631 -4.98597 0 -0 0</wrench>
        </link>
      </model>
      <model name='cart_front_steer_0'>
        <pose>-12.7491 13.257 0 0 1e-06 0.447337</pose>
        <scale>1 1 1</scale>
        <link name='chassis'>
          <pose>-12.7491 13.257 0.15 0 1e-06 0.447337</pose>
          <velocity>0.004347 0.00205 -6e-06 -3.7e-05 -6e-06 -5.9e-05</velocity>
          <acceleration>-0.001055 0.001363 0.000498 0.001506 0.000457 0.002509</acceleration>
          <wrench>-0.010554 0.013633 0.004978 0 -0 0</wrench>
        </link>
        <link name='wheel_front_left'>
          <pose>-12.4272 13.7419 0.15 -1.57083 -1.29366 0.446491</pose>
          <velocity>0.00438 0.002022 -2.8e-05 -0.01456 0.03195 -0.011356</velocity>
          <acceleration>-0.002702 0.003165 0.001503 0.023353 -0.110885 1.32855</acceleration>
          <wrench>-0.001351 0.001583 0.000752 0 -0 0</wrench>
        </link>
        <link name='wheel_front_right'>
          <pose>-12.1694 13.2046 0.15 1.57079 -0.169402 -2.69349</pose>
          <velocity>0.00434 0.002028 4e-06 -0.01389 0.029181 0.001024</velocity>
          <acceleration>-0.000244 0.000969 9.9e-05 0.013158 -0.043234 -0.569269</acceleration>
          <wrench>-0.000122 0.000485 4.9e-05 0 -0 0</wrench>
        </link>
        <link name='wheel_rear_left'>
          <pose>-13.3288 13.3093 0.150001 -1.57084 1.10888 0.4473</pose>
          <velocity>0.00435 0.002085 -1e-06 -0.013862 0.029015 -6e-05</velocity>
          <acceleration>-0.001078 -0.000323 1.7e-05 0.000787 -0.004509 0.004613</acceleration>
          <wrench>-0.000539 -0.000162 8e-06 0 -0 0</wrench>
        </link>
        <link name='wheel_rear_right'>
          <pose>-13.071 12.772 0.15 -1.57076 1.18629 0.447367</pose>
          <velocity>0.004319 0.002068 -1e-06 -0.01382 0.028779 -5.9e-05</velocity>
          <acceleration>0.000284 0.000322 -7e-06 -0.001714 0.001167 0.004661</acceleration>
          <wrench>0.000142 0.000161 -4e-06 0 -0 0</wrench>
        </link>
      </model>
      <model name='cart_rigid_suspension'>
        <pose>-11.976 11.5605 0 0 -0 -0.765174</pose>
        <scale>1 1 1</scale>
        <link name='chassis'>
          <pose>-11.976 11.5605 0.15 0 -0 -0.765174</pose>
          <velocity>-0.000116 0.000395 0.004999 0.00691 0.007232 -0.000511</velocity>
          <acceleration>0.185286 0.192668 9.68803 1.25314 -0.973968 2.33788</acceleration>
          <wrench>1.85286 1.92668 96.8803 0 -0 0</wrench>
        </link>
        <link name='wheel_front_left'>
          <pose>-11.4089 11.4291 0.15 -1.57081 -0.363875 -0.765171</pose>
          <velocity>-0.000169 0.000104 6e-06 -0.000571 -0.001014 -0.00059</velocity>
          <acceleration>0.150549 -0.208513 0.006548 0.955536 0.551688 -1.63647</acceleration>
          <wrench>0.075275 -0.104256 0.003274 0 -0 0</wrench>
        </link>
        <link name='wheel_front_right'>
          <pose>-11.8218 10.9993 0.15 -1.57079 -0.828091 -0.765177</pose>
          <velocity>-0.000404 0.00033 -8e-06 -0.002322 -0.0028 -0.000583</velocity>
          <acceleration>-0.212352 0.142273 -0.008142 -0.517638 -0.963199 -1.6236</acceleration>
          <wrench>-0.106176 0.071136 -0.004071 0 -0 0</wrench>
        </link>
        <link name='wheel_rear_left'>
          <pose>-12.1302 12.1218 0.15 -1.5708 -0.356942 -0.765173</pose>
          <velocity>0.000114 0.000409 0.009996 -0.002736 0.000748 -0.000311</velocity>
          <acceleration>0.62999 0.305657 24.1933 2.15367 0.857079 2.10425</acceleration>
          <wrench>0.314995 0.152829 12.0967 0 -0 0</wrench>
        </link>
        <link name='wheel_rear_right'>
          <pose>-12.543 11.6919 0.15 -1.5708 -0.828552 -0.76517</pose>
          <velocity>-0.00011 0.000636 0.009997 -0.004249 -0.000745 -0.000301</velocity>
          <acceleration>0.279752 0.653416 24.2518 -2.34897 0.942614 -1.05614</acceleration>
          <wrench>0.139876 0.326708 12.1259 0 -0 0</wrench>
        </link>
      </model>
      <model name='complex_env3'>
        <pose>-7.7367 20.0281 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='Wall_1'>
          <pose>12.2078 -0.084301 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_10'>
          <pose>4.58279 2.6407 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_11'>
          <pose>0.657784 4.1907 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_12'>
          <pose>0.247264 5.8507 0 0 -0 2.87979</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_13'>
          <pose>-0.163256 6.6357 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_14'>
          <pose>4.01179 7.3107 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_15'>
          <pose>8.18679 8.4857 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_16'>
          <pose>3.38679 9.6607 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_17'>
          <pose>-1.41326 7.1107 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_18'>
          <pose>-3.96326 4.5607 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_19'>
          <pose>-6.51326 7.1107 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_2'>
          <pose>10.5328 -5.5093 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_20'>
          <pose>-10.0633 9.6607 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_22'>
          <pose>-11.701 9.0812 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_23'>
          <pose>-9.66855 6.4062 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_24'>
          <pose>-8.86855 3.7312 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_25'>
          <pose>-8.06855 0.556199 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_26'>
          <pose>-11.6185 -2.6188 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_27'>
          <pose>-15.1685 0.431199 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_28'>
          <pose>-13.9935 3.4812 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_29'>
          <pose>-12.8185 5.4062 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_3'>
          <pose>8.85779 -4.2093 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_30'>
          <pose>-14.4935 7.3312 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_31'>
          <pose>-16.1685 8.6312 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_32'>
          <pose>-19.8435 9.9312 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_4'>
          <pose>8.30779 -2.9093 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_41'>
          <pose>-13.7334 9.3812 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_43'>
          <pose>12.2078 8.6407 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_44'>
          <pose>14.1328 11.9407 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_45'>
          <pose>16.0578 13.6157 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_46'>
          <pose>16.0578 17.4657 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_47'>
          <pose>10.0078 19.6407 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_48'>
          <pose>3.95779 17.2157 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_49'>
          <pose>5.88279 14.7907 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_5'>
          <pose>7.75779 -2.7343 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_50'>
          <pose>7.80779 14.1157 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_51'>
          <pose>3.25779 13.4407 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_52'>
          <pose>-1.85515 15.5416 0 0 -0 1.8326</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_53'>
          <pose>-13.0931 17.6425 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_54'>
          <pose>-23.7681 19.9425 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_55'>
          <pose>-22.3431 22.2425 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_56'>
          <pose>-20.9181 28.9175 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_57'>
          <pose>-22.1738 35.929 0 0 -0 2.87979</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_58'>
          <pose>-23.4295 37.8155 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_59'>
          <pose>-23.4295 40.2905 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_6'>
          <pose>2.95779 -2.5593 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_60'>
          <pose>-20.8795 41.2155 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_61'>
          <pose>-18.3295 43.3905 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_62'>
          <pose>-23.6295 45.5655 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_63'>
          <pose>-28.9295 36.1405 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_64'>
          <pose>-30.2295 26.7154 0 0 -0 3.14159</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_65'>
          <pose>-31.5295 18.4154 0 0 0 -1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_66'>
          <pose>-27.524 10.0233 0 0 0 -0.022998</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_7'>
          <pose>-1.84222 -0.134301 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_8'>
          <pose>3.33279 2.2907 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
        <link name='Wall_9'>
          <pose>8.50779 2.4657 0 0 -0 1.5708</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='cube_20k'>
        <pose>9.41567 5.14956 -0 0 1e-06 -0.650085</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>9.41567 5.14956 0.5 0 1e-06 -0.650085</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0.877118 -0.623557 -0.198503 -1.89647 1.38742 3.13823</acceleration>
          <wrench>0.877118 -0.623557 -0.198503 0 -0 0</wrench>
        </link>
      </model>
      <model name='double_pendulum_with_base_2'>
        <pose>-3.84296 15.2744 -0 0 1e-06 -0.077361</pose>
        <scale>1 1 1</scale>
        <link name='base'>
          <pose>-3.84296 15.2744 -0 0 1e-06 -0.077361</pose>
          <velocity>0 -0 -0.000158 5.9e-05 0.000956 0</velocity>
          <acceleration>0.000126 -3e-06 -1.5383 2.17244 0.010581 6.3e-05</acceleration>
          <wrench>0.012564 -0.00029 -153.83 0 -0 0</wrench>
        </link>
        <link name='lower_link'>
          <pose>-3.62273 14.8806 1.17319 2.63846 1e-06 -0.077361</pose>
          <velocity>-0.075947 -0.995672 0.40239 2.09263 -0.16111 2.7e-05</velocity>
          <acceleration>9.5566 6.48522 0.393043 0.273047 -0.175636 0.049164</acceleration>
          <wrench>9.5566 6.48522 0.393043 0 -0 0</wrench>
        </link>
        <link name='upper_link'>
          <pose>-3.84295 15.2744 2.1 2.75661 1e-06 -0.077361</pose>
          <velocity>0.002179 -0.00025 -0.000422 -1.07335 0.08428 1.9e-05</velocity>
          <acceleration>20.7253 6.95907 -0.072245 2.25282 0.131791 -0.010476</acceleration>
          <wrench>20.7253 6.95907 -0.072245 0 -0 0</wrench>
        </link>
      </model>
      <model name='double_pendulum_with_base_3'>
        <pose>-3.41869 10.7915 0 0 0 -0.0373</pose>
        <scale>1 1 1</scale>
        <link name='base'>
          <pose>-3.41869 10.7915 0 0 0 -0.0373</pose>
          <velocity>-0 -0 0.001326 -0.000506 -0.00801 0</velocity>
          <acceleration>-0.004813 0.000711 2.6367 2.16221 0.331047 -3.13459</acceleration>
          <wrench>-0.481275 0.071143 263.67 0 -0 0</wrench>
        </link>
        <link name='lower_link'>
          <pose>-3.18276 10.4097 1.17206 -2.86899 0 -0.0373</pose>
          <velocity>-0.002058 0.197585 -0.075836 0.407427 -0.023268 -9e-06</velocity>
          <acceleration>-11.0253 3.19933 4.94469 -0.787684 0.457736 3.11431</acceleration>
          <wrench>-11.0253 3.19933 4.94469 0 -0 0</wrench>
        </link>
        <link name='upper_link'>
          <pose>-3.41869 10.7915 2.1 2.75965 -0 -0.0373</pose>
          <velocity>-0.016863 0.001029 0.001316 0.211819 -0.015962 -5e-06</velocity>
          <acceleration>-26.3757 3.58827 1.93414 0.143461 0.687345 3.11999</acceleration>
          <wrench>-26.3757 3.58827 1.93414 0 -0 0</wrench>
        </link>
      </model>
      <model name='fire_hydrant'>
        <pose>-19.9411 15.1637 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-19.9411 15.1637 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='fire_hydrant_0'>
        <pose>-25.643 13.3813 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-25.643 13.3813 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='fountain'>
        <pose>-3.06862 6.88735 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-3.06862 6.88735 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='ground_plane'>
        <pose>0 0 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>0 0 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='hatchback_blue'>
        <pose>-15.9357 15.9493 0 0 0 -0.90473</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-15.9357 15.9493 0 0 0 -0.90473</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='hatchback_red'>
        <pose>-23.9301 11.6127 0 0 -0 0.457948</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-23.9301 11.6127 0 0 -0 0.457948</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='husky'>
        <pose>-7.57724 13.7428 0.142755 2e-06 -2e-06 -0.300116</pose>
        <scale>1 1 1</scale>
        <link name='back_left_wheel'>
          <pose>-7.7374 14.0912 0.177755 -1.5e-05 0.247922 -0.300118</pose>
          <velocity>0.001661 -6.6e-05 0.005073 0.000311 0.008701 -0.001928</velocity>
          <acceleration>-4.22003 -0.594524 9.58676 0.563719 -0.044997 -0.156008</acceleration>
          <wrench>-11.1227 -1.56699 25.2678 0 -0 0</wrench>
        </link>
        <link name='back_right_wheel'>
          <pose>-7.90619 13.5458 0.177754 3.14158 0.621807 2.84147</pose>
          <velocity>0.0003 0.000331 0.003658 -0.001489 0.002766 -0.003577</velocity>
          <acceleration>-3.56448 -0.471091 10.4946 -0.895731 1.04727 -3.00748</acceleration>
          <wrench>-9.39489 -1.24165 27.6606 0 -0 0</wrench>
        </link>
        <link name='base_link'>
          <pose>-7.57724 13.7428 0.142755 2e-06 -2e-06 -0.300116</pose>
          <velocity>0.000573 -8.9e-05 0.00195 0.004803 0.008464 -0.002446</velocity>
          <acceleration>0.903768 -1.19518 6.04114 -1.8693 -1.56376 -0.691206</acceleration>
          <wrench>30.5971 -40.4627 204.523 0 -0 0</wrench>
        </link>
        <link name='front_left_wheel'>
          <pose>-7.24829 13.9399 0.177756 -3.14159 -0.095925 2.84148</pose>
          <velocity>0.001381 -0.001007 9.4e-05 0.005904 0.00855 -0.00253</velocity>
          <acceleration>-3.83292 0.556087 -2.62803 -0.210314 -0.254177 -2.52529</acceleration>
          <wrench>-10.1024 1.46568 -6.9267 0 -0 0</wrench>
        </link>
        <link name='front_right_wheel'>
          <pose>-7.41708 13.3944 0.177755 2e-06 -0.133106 -0.300116</pose>
          <velocity>8e-06 -0.00065 -0.001062 0.007298 0.017018 -0.002479</velocity>
          <acceleration>-3.339 0.323355 -1.33946 2.21235 -0.684635 1.38647</acceleration>
          <wrench>-8.8006 0.852268 -3.53042 0 -0 0</wrench>
        </link>
      </model>
      <model name='husky_0'>
        <pose>-6.32627 15.4718 0.142753 5e-06 -2e-06 1.18285</pose>
        <scale>1 1 1</scale>
        <link name='back_left_wheel'>
          <pose>-6.68738 15.3428 0.177755 -1.2e-05 -0.34835 1.18285</pose>
          <velocity>-0.001896 -0.003602 0.002576 0.020702 -0.010766 -0.001365</velocity>
          <acceleration>-0.283868 -1.39614 5.12812 3.02351 0.100203 1.30817</acceleration>
          <wrench>-0.748192 -3.6798 13.5162 0 -0 0</wrench>
        </link>
        <link name='back_right_wheel'>
          <pose>-6.15885 15.1269 0.177751 -2.7e-05 1.43298 1.18282</pose>
          <velocity>-0.002 -0.004177 0.002135 0.023927 -0.011244 -0.000928</velocity>
          <acceleration>-0.130513 -1.50245 4.28521 0.592505 0.556478 0.310677</acceleration>
          <wrench>-0.343994 -3.96001 11.2945 0 -0 0</wrench>
        </link>
        <link name='base_link'>
          <pose>-6.32627 15.4718 0.142753 5e-06 -2e-06 1.18285</pose>
          <velocity>-0.001933 -0.004062 0.00118 -0.004162 0.001979 -0.00096</velocity>
          <acceleration>0.266229 0.390596 3.1448 1.21697 -1.31628 -1.41239</acceleration>
          <wrench>9.01317 13.2236 106.467 0 -0 0</wrench>
        </link>
        <link name='front_left_wheel'>
          <pose>-6.49369 15.8168 0.177754 -3.14159 -0.676751 -1.95875</pose>
          <velocity>-0.001538 -0.003741 -3.1e-05 0.020794 -0.008521 -0.000896</velocity>
          <acceleration>-0.540291 -1.27655 -0.06038 -2.22285 -0.04665 -2.93926</acceleration>
          <wrench>-1.42405 -3.3646 -0.159143 0 -0 0</wrench>
        </link>
        <link name='front_right_wheel'>
          <pose>-5.96517 15.6008 0.177753 2.7e-05 -1.17298 1.18283</pose>
          <velocity>-0.001748 -0.004266 4.2e-05 0.024286 -0.010001 -0.001078</velocity>
          <acceleration>-0.549248 -1.30174 0.087137 -2.05293 -0.006685 2.80422</acceleration>
          <wrench>-1.44765 -3.431 0.229667 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box'>
        <pose>8.58499 0.438001 0.5 0 -0 0.641361</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>8.58499 0.438001 0.5 0 -0 0.641361</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-3e-06 -6.5e-05 1e-06 0.000131 -5e-06 0</acceleration>
          <wrench>-3e-06 -6.5e-05 1e-06 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_0'>
        <pose>4.85833 -1.98403 0.499995 0 -1e-05 0.000539</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>4.85833 -1.98403 0.499995 0 -1e-05 0.000539</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.010618 0.006191 -9.78231 -0.012425 -0.021232 1.9e-05</acceleration>
          <wrench>-0.010618 0.006191 -9.78231 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_10'>
        <pose>-30.9397 21.2031 0.5 0 -0 0.009314</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-30.9397 21.2031 0.5 0 -0 0.009314</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-9.78161 -0.084964 9.78237 0.169867 -0.713659 -7e-05</acceleration>
          <wrench>-9.78161 -0.084964 9.78237 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_11'>
        <pose>-29.9394 21.2525 0.5 0 -0 0.009495</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-29.9394 21.2525 0.5 0 -0 0.009495</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.088166 9.78074 9.78164 -0.711914 -0.176334 -4.3e-05</acceleration>
          <wrench>-0.088166 9.78074 9.78164 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_12'>
        <pose>-28.9381 21.1597 0.5 0 -0 0.009902</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-28.9381 21.1597 0.5 0 -0 0.009902</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-9.78156 -0.090718 9.78237 0.181375 -0.71356 -7e-05</acceleration>
          <wrench>-9.78156 -0.090718 9.78237 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_13'>
        <pose>-27.7219 18.9013 0.5 0 -0 0.064363</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-27.7219 18.9013 0.5 0 -0 0.064363</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-1e-06 0 0 -1e-06 -1e-06 0</acceleration>
          <wrench>-1e-06 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_14'>
        <pose>-26.7267 19.0169 0.499995 0 1e-05 0.064981</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-26.7267 19.0169 0.499995 0 1e-05 0.064981</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0.010828 -0.006003 -9.78265 0.012145 0.021647 6.3e-05</acceleration>
          <wrench>0.010828 -0.006003 -9.78265 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_15'>
        <pose>-25.747 19.3641 0.5 0 -0 0.064685</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-25.747 19.3641 0.5 0 -0 0.064685</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-9.76149 -0.638458 9.78239 1.27691 -0.673407 4.3e-05</acceleration>
          <wrench>-9.76149 -0.638458 9.78239 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_16'>
        <pose>-24.7292 19.1355 0.5 0 -0 0.060436</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-24.7292 19.1355 0.5 0 -0 0.060436</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0 -1e-06 -0 1e-06 -1e-06 0</acceleration>
          <wrench>-0 -1e-06 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_17'>
        <pose>-27.0016 44.5815 0.5 -0 -0 -5e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-27.0016 44.5815 0.5 -0 -0 -5e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.004761 -9.78112 9.78158 0.712677 -0.009518 -4.3e-05</acceleration>
          <wrench>-0.004761 -9.78112 9.78158 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_18'>
        <pose>-25.5631 41.7649 0.499997 0 5e-06 0.12837</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-25.5631 41.7649 0.499997 0 5e-06 0.12837</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>4.45327 -1.04237 -2.66177 -1.04913 0.51856 -3.1367</acceleration>
          <wrench>4.45327 -1.04237 -2.66177 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_19'>
        <pose>-27.0831 38.8535 0.5 -0 -0 -5e-06</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-27.0831 38.8535 0.5 -0 -0 -5e-06</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.004761 -9.78112 9.78158 0.712677 -0.009518 -4.3e-05</acceleration>
          <wrench>-0.004761 -9.78112 9.78158 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_2'>
        <pose>-5.87193 8.92366 0.499995 -0 1e-05 0.122908</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-5.87193 8.92366 0.499995 -0 1e-05 0.122908</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0.009135 0.005459 -9.78377 -0.010913 0.018279 4e-06</acceleration>
          <wrench>0.009135 0.005459 -9.78377 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_20'>
        <pose>-24.0679 25.3728 0.5 -0 -0 -0.005076</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-24.0679 25.3728 0.5 -0 -0 -0.005076</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.054358 -9.78097 9.78155 0.712377 -0.108711 -4.3e-05</acceleration>
          <wrench>-0.054358 -9.78097 9.78155 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_21'>
        <pose>10.5539 9.06141 0.5 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>10.5539 9.06141 0.5 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-9.78197 0.006192 9.78231 -0.012424 -0.714369 -6.1e-05</acceleration>
          <wrench>-9.78197 0.006192 9.78231 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_23'>
        <pose>9.88812 11.8477 0.5 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>9.88812 11.8477 0.5 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-9.78197 0.006192 9.78231 -0.012424 -0.714369 -6.1e-05</acceleration>
          <wrench>-9.78197 0.006192 9.78231 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_4'>
        <pose>-29.466 15.6921 0.499995 -1e-05 -0 0.020412</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-29.466 15.6921 0.499995 -1e-05 -0 0.020412</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0.004726 0.01114 -9.7816 -0.022276 0.009449 1e-06</acceleration>
          <wrench>0.004726 0.01114 -9.7816 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_5'>
        <pose>-28.4661 15.7075 0.5 0 -0 0.020412</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-28.4661 15.7075 0.5 0 -0 0.020412</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.194943 9.77923 9.78174 -0.708907 -0.389888 -4.3e-05</acceleration>
          <wrench>-0.194943 9.77923 9.78174 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_6'>
        <pose>-27.4639 15.6221 0.5 0 -0 0.020525</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-27.4639 15.6221 0.5 0 -0 0.020525</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.196051 9.77921 9.78174 -0.708864 -0.392103 -4.3e-05</acceleration>
          <wrench>-0.196051 9.77921 9.78174 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_7'>
        <pose>-24.3975 17.1188 0.5 0 -0 0.024781</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-24.3975 17.1188 0.5 0 -0 0.024781</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -0 -1e-06 1e-06 0</acceleration>
          <wrench>0 0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_8'>
        <pose>-25.4725 16.0922 0.5 0 -0 0.021424</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-25.4725 16.0922 0.5 0 -0 0.021424</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.204844 9.77904 9.78175 -0.708514 -0.409688 -4.3e-05</acceleration>
          <wrench>-0.204844 9.77904 9.78175 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_box_9'>
        <pose>-26.4692 15.8946 0.5 0 -0 0.020705</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-26.4692 15.8946 0.5 0 -0 0.020705</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 -0 -1e-06 1e-06 0</acceleration>
          <wrench>0 0 -0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder'>
        <pose>4.95476 1.27157 0.499996 -1.5708 0.000138 0.48701</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>4.95476 1.27157 0.499996 -1.5708 0.000138 0.48701</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-1.49609 2.8252 -4.73826 -2.50885 -0.149376 -3.14159</acceleration>
          <wrench>-1.49609 2.8252 -4.73826 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder_0'>
        <pose>-25.6566 35.1955 0.5 1.57029 -1.57045 -2.16863</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-25.6566 35.1955 0.5 1.57029 -1.57045 -2.16863</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-7.86572 5.37759 9.57047 -1.28559 0.109964 3.1274</acceleration>
          <wrench>-7.86572 5.37759 9.57047 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder_1'>
        <pose>-26.9878 32.212 0.5 -1.57072 1.5687 -0.99423</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-26.9878 32.212 0.5 -1.57072 1.5687 -0.99423</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>7.94315 5.17428 9.50613 -0.899489 -0.243397 -3.1143</acceleration>
          <wrench>7.94315 5.17428 9.50613 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder_2'>
        <pose>-25.2311 29.5284 0.499995 -1.57704 -1.56928 0.731236</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-25.2311 29.5284 0.499995 -1.57704 -1.56928 0.731236</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-0.141688 0.1582 -9.48415 -0.269112 -0.231271 -0.003369</acceleration>
          <wrench>-0.141688 0.1582 -9.48415 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder_3'>
        <pose>-26.5012 27.1904 0.5 -1.57039 1.57034 -0.648526</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-26.5012 27.1904 0.5 -1.57039 1.57034 -0.648526</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>5.76369 7.57108 9.56703 -2.68807 -1.0932 -0.014632</acceleration>
          <wrench>5.76369 7.57108 9.56703 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder_4'>
        <pose>-23.4079 31.767 0.5 -1.57052 -1.57021 2.10634</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-23.4079 31.767 0.5 -1.57052 -1.57021 2.10634</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-8.16616 -4.85619 9.52278 0.26826 0.675233 -3.1187</acceleration>
          <wrench>-8.16616 -4.85619 9.52278 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder_5'>
        <pose>-28.4927 24.9556 0.5 -1.5702 1.5705 -0.599325</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-28.4927 24.9556 0.5 -1.5702 1.5705 -0.599325</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>5.39145 7.85935 9.57376 -0.097153 -1.31361 3.13016</acceleration>
          <wrench>5.39145 7.85935 9.57376 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_sphere'>
        <pose>0.887488 -1.57296 0.5 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>0.887488 -1.57296 0.5 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_sphere_0'>
        <pose>-7.65578 10.8174 0.5 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-7.65578 10.8174 0.5 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_sphere_1'>
        <pose>-23.0621 25.3566 0.5 0.002604 0.011757 0.007953</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-23.0621 25.3566 0.5 0.002604 0.011757 0.007953</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_sphere_2'>
        <pose>-23.9841 24.2817 0.5 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-23.9841 24.2817 0.5 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <light name='sun'>
        <pose>0 0 10 0 -0 0</pose>
      </light>
    </state>
    <model name='fountain'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://fountain/meshes/fountain.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://fountain/meshes/fountain.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-3.06862 6.88735 0 0 -0 0</pose>
    </model>
    <model name='unit_box_2'>
      <pose>-6.18535 9.03682 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_sphere_0'>
      <pose>-7.65578 10.8174 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.1</iyy>
            <iyz>0</iyz>
            <izz>0.1</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='husky'>
      <pose>-7.74164 13.076 0.5 0 -0 0</pose>
      <link name='base_link'>
        <collision name='collision'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.0074 0.5709 0.2675</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <inertial>
          <mass>33.855</mass>
          <pose>-0.085613 -0.00084 0.238145 0 -0 0</pose>
          <inertia>
            <ixx>2.2343</ixx>
            <ixy>-0.023642</ixy>
            <ixz>0.275174</ixz>
            <iyy>3.42518</iyy>
            <iyz>0.00239624</iyz>
            <izz>2.1241</izz>
          </inertia>
        </inertial>
        <visual name='base'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/base_link.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/FlatBlack</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <visual name='top_plate'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/top_plate.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Yellow</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <visual name='front_bumper'>
          <pose>0.47 0 0.091 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/bumper.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/FlatBlack</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <visual name='rear_bumper'>
          <pose>-0.47 0 0.091 0 -0 3.141</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/bumper.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/FlatBlack</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <visual name='user_rail'>
          <pose>0.272 0 0.245 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/user_rail.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='back_left_wheel'>
        <pose>-0.256 0.285475 0.035 0 -0 0</pose>
        <inertial>
          <mass>2.6357</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>0.0246688</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0246688</iyy>
            <iyz>0</iyz>
            <izz>0.0441058</izz>
          </inertia>
        </inertial>
        <collision name='back_left_wheel_collision'>
          <pose>0 0 0 -1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.17775</radius>
              <length>0.1143</length>
            </cylinder>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000</mu>
                <mu2>100000</mu2>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='back_left_wheel'>
          <pose>0 0 0 -3.14159 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/wheel.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='back_left_joint' type='revolute'>
        <parent>base_link</parent>
        <child>back_left_wheel</child>
        <axis>
          <xyz expressed_in='__model__'>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <physics>
          <ode>
            <limit>
              <cfm>0</cfm>
              <erp>0.9</erp>
            </limit>
          </ode>
        </physics>
      </joint>
      <link name='back_right_wheel'>
        <pose>-0.256 -0.285475 0.035 0 -0 0</pose>
        <inertial>
          <mass>2.6357</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>0.0246688</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0246688</iyy>
            <iyz>0</iyz>
            <izz>0.0441058</izz>
          </inertia>
        </inertial>
        <collision name='back_right_wheel_collision'>
          <pose>0 0 0 -1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.17775</radius>
              <length>0.1143</length>
            </cylinder>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000</mu>
                <mu2>100000</mu2>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='back_right_wheel'>
          <pose>0 0 0 -3.14159 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/wheel.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='back_right_joint' type='revolute'>
        <parent>base_link</parent>
        <child>back_right_wheel</child>
        <axis>
          <xyz expressed_in='__model__'>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <physics>
          <ode>
            <limit>
              <cfm>0</cfm>
              <erp>0.9</erp>
            </limit>
          </ode>
        </physics>
      </joint>
      <link name='front_left_wheel'>
        <pose>0.256 0.285475 0.035 0 -0 0</pose>
        <inertial>
          <mass>2.6357</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>0.0246688</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0246688</iyy>
            <iyz>0</iyz>
            <izz>0.0441058</izz>
          </inertia>
        </inertial>
        <collision name='front_left_wheel_collision'>
          <pose>0 0 0 -1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.17775</radius>
              <length>0.1143</length>
            </cylinder>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000</mu>
                <mu2>100000</mu2>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='front_left_wheel'>
          <pose>0 0 0 -3.14159 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/wheel.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='front_left_joint' type='revolute'>
        <parent>base_link</parent>
        <child>front_left_wheel</child>
        <axis>
          <xyz expressed_in='__model__'>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <physics>
          <ode>
            <limit>
              <cfm>0</cfm>
              <erp>0.9</erp>
            </limit>
          </ode>
        </physics>
      </joint>
      <link name='front_right_wheel'>
        <pose>0.256 -0.285475 0.035 0 -0 0</pose>
        <inertial>
          <mass>2.6357</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>0.0246688</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0246688</iyy>
            <iyz>0</iyz>
            <izz>0.0441058</izz>
          </inertia>
        </inertial>
        <collision name='front_right_wheel_collision'>
          <pose>0 0 0 -1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.17775</radius>
              <length>0.1143</length>
            </cylinder>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000</mu>
                <mu2>100000</mu2>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='front_right_wheel'>
          <pose>0 0 0 -3.14159 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/wheel.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='front_right_joint' type='revolute'>
        <parent>base_link</parent>
        <child>front_right_wheel</child>
        <axis>
          <xyz expressed_in='__model__'>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <physics>
          <ode>
            <limit>
              <cfm>0</cfm>
              <erp>0.9</erp>
            </limit>
          </ode>
        </physics>
      </joint>
      <plugin name='husky_diff_controller' filename='libhusky_gazebo_plugins.so'>
        <alwaysOn>1</alwaysOn>
        <updateRate>100.0</updateRate>
        <backLeftJoint>back_left_joint</backLeftJoint>
        <backRightJoint>back_right_joint</backRightJoint>
        <frontLeftJoint>front_left_joint</frontLeftJoint>
        <frontRightJoint>front_right_joint</frontRightJoint>
        <wheelSeparation>0.5709</wheelSeparation>
        <wheelDiameter>0.3555</wheelDiameter>
        <torque>35</torque>
      </plugin>
    </model>
    <model name='husky_0'>
      <pose>-7.21704 14.9621 0.5 0 -0 0</pose>
      <link name='base_link'>
        <collision name='collision'>
          <pose>0 0 0.1 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.0074 0.5709 0.2675</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <inertial>
          <mass>33.855</mass>
          <pose>-0.085613 -0.00084 0.238145 0 -0 0</pose>
          <inertia>
            <ixx>2.2343</ixx>
            <ixy>-0.023642</ixy>
            <ixz>0.275174</ixz>
            <iyy>3.42518</iyy>
            <iyz>0.00239624</iyz>
            <izz>2.1241</izz>
          </inertia>
        </inertial>
        <visual name='base'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/base_link.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/FlatBlack</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <visual name='top_plate'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/top_plate.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Yellow</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <visual name='front_bumper'>
          <pose>0.47 0 0.091 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/bumper.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/FlatBlack</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <visual name='rear_bumper'>
          <pose>-0.47 0 0.091 0 -0 3.141</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/bumper.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/FlatBlack</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <visual name='user_rail'>
          <pose>0.272 0 0.245 0 -0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/user_rail.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='back_left_wheel'>
        <pose>-0.256 0.285475 0.035 0 -0 0</pose>
        <inertial>
          <mass>2.6357</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>0.0246688</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0246688</iyy>
            <iyz>0</iyz>
            <izz>0.0441058</izz>
          </inertia>
        </inertial>
        <collision name='back_left_wheel_collision'>
          <pose>0 0 0 -1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.17775</radius>
              <length>0.1143</length>
            </cylinder>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000</mu>
                <mu2>100000</mu2>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='back_left_wheel'>
          <pose>0 0 0 -3.14159 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/wheel.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='back_left_joint' type='revolute'>
        <parent>base_link</parent>
        <child>back_left_wheel</child>
        <axis>
          <xyz expressed_in='__model__'>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <physics>
          <ode>
            <limit>
              <cfm>0</cfm>
              <erp>0.9</erp>
            </limit>
          </ode>
        </physics>
      </joint>
      <link name='back_right_wheel'>
        <pose>-0.256 -0.285475 0.035 0 -0 0</pose>
        <inertial>
          <mass>2.6357</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>0.0246688</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0246688</iyy>
            <iyz>0</iyz>
            <izz>0.0441058</izz>
          </inertia>
        </inertial>
        <collision name='back_right_wheel_collision'>
          <pose>0 0 0 -1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.17775</radius>
              <length>0.1143</length>
            </cylinder>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000</mu>
                <mu2>100000</mu2>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='back_right_wheel'>
          <pose>0 0 0 -3.14159 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/wheel.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='back_right_joint' type='revolute'>
        <parent>base_link</parent>
        <child>back_right_wheel</child>
        <axis>
          <xyz expressed_in='__model__'>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <physics>
          <ode>
            <limit>
              <cfm>0</cfm>
              <erp>0.9</erp>
            </limit>
          </ode>
        </physics>
      </joint>
      <link name='front_left_wheel'>
        <pose>0.256 0.285475 0.035 0 -0 0</pose>
        <inertial>
          <mass>2.6357</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>0.0246688</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0246688</iyy>
            <iyz>0</iyz>
            <izz>0.0441058</izz>
          </inertia>
        </inertial>
        <collision name='front_left_wheel_collision'>
          <pose>0 0 0 -1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.17775</radius>
              <length>0.1143</length>
            </cylinder>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000</mu>
                <mu2>100000</mu2>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='front_left_wheel'>
          <pose>0 0 0 -3.14159 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/wheel.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='front_left_joint' type='revolute'>
        <parent>base_link</parent>
        <child>front_left_wheel</child>
        <axis>
          <xyz expressed_in='__model__'>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <physics>
          <ode>
            <limit>
              <cfm>0</cfm>
              <erp>0.9</erp>
            </limit>
          </ode>
        </physics>
      </joint>
      <link name='front_right_wheel'>
        <pose>0.256 -0.285475 0.035 0 -0 0</pose>
        <inertial>
          <mass>2.6357</mass>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>0.0246688</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.0246688</iyy>
            <iyz>0</iyz>
            <izz>0.0441058</izz>
          </inertia>
        </inertial>
        <collision name='front_right_wheel_collision'>
          <pose>0 0 0 -1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.17775</radius>
              <length>0.1143</length>
            </cylinder>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100000</mu>
                <mu2>100000</mu2>
                <slip1>0</slip1>
                <slip2>0</slip2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='front_right_wheel'>
          <pose>0 0 0 -3.14159 0 0</pose>
          <geometry>
            <mesh>
              <uri>model://husky/meshes/wheel.stl</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>__default__</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='front_right_joint' type='revolute'>
        <parent>base_link</parent>
        <child>front_right_wheel</child>
        <axis>
          <xyz expressed_in='__model__'>0 1 0</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <physics>
          <ode>
            <limit>
              <cfm>0</cfm>
              <erp>0.9</erp>
            </limit>
          </ode>
        </physics>
      </joint>
      <plugin name='husky_diff_controller' filename='libhusky_gazebo_plugins.so'>
        <alwaysOn>1</alwaysOn>
        <updateRate>100.0</updateRate>
        <backLeftJoint>back_left_joint</backLeftJoint>
        <backRightJoint>back_right_joint</backRightJoint>
        <frontLeftJoint>front_left_joint</frontLeftJoint>
        <frontRightJoint>front_right_joint</frontRightJoint>
        <wheelSeparation>0.5709</wheelSeparation>
        <wheelDiameter>0.3555</wheelDiameter>
        <torque>35</torque>
      </plugin>
    </model>
    <model name='unit_box_4'>
      <pose>-29.3379 15.701 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_5'>
      <pose>-28.3969 15.6988 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_6'>
      <pose>-27.3733 15.6761 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_7'>
      <pose>-24.6546 17.117 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_8'>
      <pose>-25.6632 16.0718 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_9'>
      <pose>-26.5803 15.8897 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_10'>
      <pose>-31.2346 21.187 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_11'>
      <pose>-30.2566 21.1871 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_12'>
      <pose>-29.287 21.1925 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_13'>
      <pose>-27.3011 18.993 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_14'>
      <pose>-26.3181 19.0435 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_15'>
      <pose>-25.356 19.0777 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_16'>
      <pose>-24.3861 18.9785 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_17'>
      <pose>-27.0016 44.5815 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_18'>
      <pose>-25.6109 41.9221 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_19'>
      <pose>-27.0831 38.8535 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_0'>
      <pose>-25.4973 35.1955 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_1'>
      <pose>-27.0641 32.2108 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_2'>
      <pose>-24.8947 29.529 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_3'>
      <pose>-26.9074 27.1904 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_4'>
      <pose>-23.2618 31.767 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_5'>
      <pose>-28.663 24.9556 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_sphere_1'>
      <pose>-23.1652 25.3587 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.1</iyy>
            <iyz>0</iyz>
            <izz>0.1</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_20'>
      <pose>-23.9707 25.3719 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_sphere_2'>
      <pose>-23.9841 24.2817 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.1</iyy>
            <iyz>0</iyz>
            <izz>0.1</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>-1.53609 -14.7963 78.8923 0 1.1778 1.51859</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
    <model name='cart_rigid_suspension'>
      <link name='chassis'>
        <pose>0 0 0.15 0 -0 0</pose>
        <inertial>
          <pose>0.1 0 0 0 -0 0</pose>
          <mass>10</mass>
          <inertia>
            <ixx>0.216667</ixx>
            <iyy>0.841667</iyy>
            <izz>1.04167</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 0.5 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='wheel_front_left'>
        <pose>0.5 0.298 0.15 -1.5708 0 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_front_left_spin' type='revolute'>
        <parent>chassis</parent>
        <child>wheel_front_left</child>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <link name='wheel_front_right'>
        <pose>0.5 -0.298 0.15 -1.5708 0 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_front_right_spin' type='revolute'>
        <parent>chassis</parent>
        <child>wheel_front_right</child>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <link name='wheel_rear_left'>
        <pose>-0.5 0.298 0.15 -1.5708 0 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_rear_left_spin' type='revolute'>
        <parent>chassis</parent>
        <child>wheel_rear_left</child>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <link name='wheel_rear_right'>
        <pose>-0.5 -0.298 0.15 -1.5708 0 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_rear_right_spin' type='revolute'>
        <parent>chassis</parent>
        <child>wheel_rear_right</child>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <pose>-12.2183 12.4873 0 0 -0 0</pose>
    </model>
    <model name='cart_front_steer_0'>
      <link name='chassis'>
        <pose>0 0 0.15 0 -0 0</pose>
        <inertial>
          <pose>0.1 0 0 0 -0 0</pose>
          <mass>10</mass>
          <inertia>
            <ixx>0.216667</ixx>
            <iyy>0.841667</iyy>
            <izz>1.04167</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 0.5 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 0.5 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <link name='wheel_front_left'>
        <pose>0.5 0.298 0.15 -1.5708 -0.087266 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_front_left_steer_spin' type='universal'>
        <parent>chassis</parent>
        <child>wheel_front_left</child>
        <axis>
          <xyz>0 -1 0</xyz>
          <limit>
            <lower>-0.610865</lower>
            <upper>0.610865</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <axis2>
          <xyz>0 0 1</xyz>
          <limit>
            <upper>1e+16</upper>
            <lower>-1e+16</lower>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis2>
      </joint>
      <link name='wheel_front_right'>
        <pose>0.5 -0.298 0.15 -1.5708 -0.087266 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_front_right_steer_spin' type='universal'>
        <parent>chassis</parent>
        <child>wheel_front_right</child>
        <axis>
          <xyz>0 -1 0</xyz>
          <limit>
            <lower>-0.610865</lower>
            <upper>0.610865</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
        <axis2>
          <xyz>0 0 1</xyz>
          <limit>
            <upper>1e+16</upper>
            <lower>-1e+16</lower>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis2>
      </joint>
      <link name='wheel_rear_left'>
        <pose>-0.5 0.298 0.15 -1.5708 0 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_rear_left_spin' type='revolute'>
        <parent>chassis</parent>
        <child>wheel_rear_left</child>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <link name='wheel_rear_right'>
        <pose>-0.5 -0.298 0.15 -1.5708 0 0</pose>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.00307917</ixx>
            <iyy>0.00307917</iyy>
            <izz>0.005625</izz>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyz>0</iyz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.15</radius>
              <length>0.08</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <joint name='wheel_rear_right_spin' type='revolute'>
        <parent>chassis</parent>
        <child>wheel_rear_right</child>
        <axis>
          <xyz>0 0 1</xyz>
          <limit>
            <lower>-1e+16</lower>
            <upper>1e+16</upper>
          </limit>
          <dynamics>
            <spring_reference>0</spring_reference>
            <spring_stiffness>0</spring_stiffness>
          </dynamics>
        </axis>
      </joint>
      <pose>-14.3356 14.0207 0 0 -0 0</pose>
    </model>
    <model name='Dumpster'>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <scale>1.5 1.5 1.5</scale>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <scale>1.5 1.5 1.5</scale>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://dumpster/materials/scripts</uri>
              <uri>model://dumpster/materials/textures</uri>
              <name>Dumpster/Diffuse</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <inertial>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-18.2822 11.5093 0 0 -0 0</pose>
    </model>
    <model name='Dumpster_0'>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <scale>1.5 1.5 1.5</scale>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <scale>1.5 1.5 1.5</scale>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://dumpster/materials/scripts</uri>
              <uri>model://dumpster/materials/textures</uri>
              <name>Dumpster/Diffuse</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <inertial>
          <pose>0 0 0 0 -0 0</pose>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-27.4107 12.7511 0 0 -0 0</pose>
    </model>
    <model name='fire_hydrant'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-19.9411 15.1637 0 0 -0 0</pose>
    </model>
    <model name='fire_hydrant_0'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://fire_hydrant/meshes/fire_hydrant.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-22.9659 13.3813 0 0 -0 0</pose>
    </model>
    <model name='hatchback_blue'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <pose>0 0 0 0 -0 1.5708</pose>
          <geometry>
            <mesh>
              <scale>0.0254 0.0254 0.0254</scale>
              <uri>model://hatchback_blue/meshes/hatchback.obj</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <pose>0 0 0 0 -0 1.5708</pose>
          <geometry>
            <mesh>
              <scale>0.0254 0.0254 0.0254</scale>
              <uri>model://hatchback_blue/meshes/hatchback.obj</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-15.9357 15.9493 0 0 -0 0</pose>
    </model>
    <model name='hatchback_red'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <pose>0 0 0 0 -0 1.5708</pose>
          <geometry>
            <mesh>
              <scale>0.0254 0.0254 0.0254</scale>
              <uri>model://hatchback_red/meshes/hatchback.obj</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <pose>0 0 0 0 -0 1.5708</pose>
          <geometry>
            <mesh>
              <scale>0.0254 0.0254 0.0254</scale>
              <uri>model://hatchback_red/meshes/hatchback.obj</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>-24.4697 11.3467 0 0 -0 0</pose>
    </model>
    <model name='unit_box_21'>
      <pose>10.5539 9.06143 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box_23'>
      <pose>9.88811 11.8477 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='camera'>
      <link name='camera'>
        <inertial>
          <pose>0 0 0 0 -0 0</pose>
          <mass>1e-05</mass>
          <inertia>
            <ixx>1e-06</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1e-06</iyy>
            <iyz>0</iyz>
            <izz>1e-06</izz>
          </inertia>
        </inertial>
        <collision name='camera_collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.001 0.001 0.001</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='camera_visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.001 0.001 0.001</size>
            </box>
          </geometry>
        </visual>
        <sensor name='camera' type='camera'>
          <update_rate>15</update_rate>
          <camera name='head'>
            <horizontal_fov>2</horizontal_fov>
            <image>
              <width>320</width>
              <height>180</height>
              <format>R8G8B8</format>
            </image>
            <clip>
              <near>0.1</near>
              <far>200</far>
            </clip>
          </camera>
          <plugin name='camera_controller' filename='libgazebo_ros_camera.so'>
            <alwaysOn>1</alwaysOn>
            <updateRate>0.0</updateRate>
            <cameraName>camera</cameraName>
            <imageTopicName>image</imageTopicName>
            <cameraInfoTopicName>camera_info</cameraInfoTopicName>
            <frameName>camera</frameName>
            <hackBaseline>0.0</hackBaseline>
            <distortionK1>0.0</distortionK1>
            <distortionK2>0.0</distortionK2>
            <distortionK3>0.0</distortionK3>
            <distortionT1>0.0</distortionT1>
            <distortionT2>0.0</distortionT2>
            <robotNamespace>/</robotNamespace>
          </plugin>
        </sensor>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>0 0 0 0 -0 0</pose>
    </model>
  </world>
</sdf>
