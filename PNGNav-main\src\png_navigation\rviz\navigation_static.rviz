Panels:
  - Class: rviz/Displays
    Help Height: 78
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
        - /Status1
        - /PointCloud21
        - /PointCloud22
      Splitter Ratio: 0.5
    Tree Height: 549
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: LaserScan
Preferences:
  PromptSaveOnExit: true
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: true
    - Class: rviz/TF
      Enabled: true
      Filter (blacklist): ""
      Filter (whitelist): ""
      Frame Timeout: 15
      Frames:
        All Enabled: true
        arm_base_link:
          Value: true
        arm_wrist_flex_link:
          Value: true
        base_footprint:
          Value: true
        base_link:
          Value: true
        camera_depth_frame:
          Value: true
        camera_depth_optical_frame:
          Value: true
        camera_link:
          Value: true
        camera_rgb_frame:
          Value: true
        camera_rgb_optical_frame:
          Value: true
        camera_sr300:
          Value: true
        camera_sr300_assembly:
          Value: true
        camera_sr300_color_frame:
          Value: true
        camera_sr300_color_optical_frame:
          Value: true
        camera_sr300_depth_frame:
          Value: true
        camera_sr300_depth_optical_frame:
          Value: true
        caster_back_link:
          Value: true
        caster_front_link:
          Value: true
        cliff_sensor_front_link:
          Value: true
        cliff_sensor_left_link:
          Value: true
        cliff_sensor_right_link:
          Value: true
        gripper_active2_link:
          Value: true
        gyro_link:
          Value: true
        laser:
          Value: true
        map:
          Value: true
        odom:
          Value: true
        plate_bottom_link:
          Value: true
        plate_middle_link:
          Value: true
        plate_top_link:
          Value: true
        plate_utility_lower_link:
          Value: true
        plate_utility_upper_link:
          Value: true
        standoff_100mm_0_link:
          Value: true
        standoff_100mm_10_link:
          Value: true
        standoff_100mm_11_link:
          Value: true
        standoff_100mm_12_link:
          Value: true
        standoff_100mm_13_link:
          Value: true
        standoff_100mm_1_link:
          Value: true
        standoff_100mm_2_link:
          Value: true
        standoff_100mm_3_link:
          Value: true
        standoff_100mm_6_link:
          Value: true
        standoff_100mm_7_link:
          Value: true
        standoff_100mm_8_link:
          Value: true
        standoff_100mm_9_link:
          Value: true
        standoff_258mm_0_link:
          Value: true
        standoff_258mm_1_link:
          Value: true
        standoff_50mm_0_link:
          Value: true
        standoff_50mm_10_link:
          Value: true
        standoff_50mm_11_link:
          Value: true
        standoff_50mm_1_link:
          Value: true
        standoff_50mm_2_link:
          Value: true
        standoff_50mm_3_link:
          Value: true
        standoff_50mm_4_link:
          Value: true
        standoff_50mm_5_link:
          Value: true
        standoff_50mm_6_link:
          Value: true
        standoff_50mm_7_link:
          Value: true
        standoff_50mm_8_link:
          Value: true
        standoff_50mm_9_link:
          Value: true
        wheel_left_link:
          Value: true
        wheel_right_link:
          Value: true
      Marker Scale: 1
      Name: TF
      Show Arrows: true
      Show Axes: true
      Show Names: true
      Tree:
        arm_wrist_flex_link:
          gripper_active2_link:
            {}
        map:
          odom:
            base_footprint:
              base_link:
                arm_base_link:
                  {}
                camera_rgb_frame:
                  camera_depth_frame:
                    camera_depth_optical_frame:
                      {}
                  camera_link:
                    {}
                  camera_rgb_optical_frame:
                    {}
                caster_back_link:
                  {}
                caster_front_link:
                  {}
                cliff_sensor_front_link:
                  {}
                cliff_sensor_left_link:
                  {}
                cliff_sensor_right_link:
                  {}
                gyro_link:
                  {}
                laser:
                  {}
                plate_bottom_link:
                  {}
                plate_middle_link:
                  {}
                plate_top_link:
                  camera_sr300_assembly:
                    camera_sr300:
                      camera_sr300_color_frame:
                        camera_sr300_color_optical_frame:
                          {}
                      camera_sr300_depth_frame:
                        camera_sr300_depth_optical_frame:
                          {}
                plate_utility_lower_link:
                  {}
                plate_utility_upper_link:
                  {}
                standoff_100mm_0_link:
                  {}
                standoff_100mm_10_link:
                  {}
                standoff_100mm_11_link:
                  {}
                standoff_100mm_12_link:
                  {}
                standoff_100mm_13_link:
                  {}
                standoff_100mm_1_link:
                  {}
                standoff_100mm_2_link:
                  {}
                standoff_100mm_3_link:
                  {}
                standoff_100mm_6_link:
                  {}
                standoff_100mm_7_link:
                  {}
                standoff_100mm_8_link:
                  {}
                standoff_100mm_9_link:
                  {}
                standoff_258mm_0_link:
                  {}
                standoff_258mm_1_link:
                  {}
                standoff_50mm_0_link:
                  {}
                standoff_50mm_10_link:
                  {}
                standoff_50mm_11_link:
                  {}
                standoff_50mm_1_link:
                  {}
                standoff_50mm_2_link:
                  {}
                standoff_50mm_3_link:
                  {}
                standoff_50mm_4_link:
                  {}
                standoff_50mm_5_link:
                  {}
                standoff_50mm_6_link:
                  {}
                standoff_50mm_7_link:
                  {}
                standoff_50mm_8_link:
                  {}
                standoff_50mm_9_link:
                  {}
                wheel_left_link:
                  {}
                wheel_right_link:
                  {}
      Update Interval: 0
      Value: true
    - Alpha: 0.699999988079071
      Class: rviz/Map
      Color Scheme: map
      Draw Behind: false
      Enabled: true
      Name: Map
      Topic: /map
      Unreliable: false
      Use Timestamp: false
      Value: true
    - Alpha: 1
      Arrow Length: 0.30000001192092896
      Axes Length: 0.30000001192092896
      Axes Radius: 0.009999999776482582
      Class: rviz/PoseArray
      Color: 255; 25; 0
      Enabled: true
      Head Length: 0.07000000029802322
      Head Radius: 0.029999999329447746
      Name: PoseArray
      Shaft Length: 0.23000000417232513
      Shaft Radius: 0.009999999776482582
      Shape: Arrow (Flat)
      Topic: /particlecloud
      Unreliable: false
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/LaserScan
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: LaserScan
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Flat Squares
      Topic: /scan
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /tree
      Name: MarkerArray
      Namespaces:
        "": true
      Queue Size: 100
      Value: true
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /png_navigation/global_plan
      Name: MarkerArray
      Namespaces:
        "": true
      Queue Size: 100
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: RGB8
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.029999999329447746
      Style: Flat Squares
      Topic: /guidance_states
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: RGB8
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Min Color: 0; 0; 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.029999999329447746
      Style: Flat Squares
      Topic: /no_guidance_states
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Default Light: true
    Fixed Frame: map
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Theta std deviation: 0.2617993950843811
      Topic: /initialpose
      X std deviation: 0.5
      Y std deviation: 0.5
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Class: rviz/Orbit
      Distance: 11.282499313354492
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: -0.2684016525745392
        Y: -0.8310416340827942
        Z: -0.006149033084511757
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.05000000074505806
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Pitch: 1.5697963237762451
      Target Frame: <Fixed Frame>
      Value: Orbit (rviz)
      Yaw: 3.1785855293273926
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 846
  Hide Left Dock: false
  Hide Right Dock: false
  QMainWindow State: 000000ff00000000fd000000040000000000000156000002b0fc0200000008fb0000001200530065006c0065006300740069006f006e00000001e10000009b0000005c00fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c006100790073010000003d000002b0000000c900fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261000000010000010f000002b0fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a00560069006500770073010000003d000002b0000000a400fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e10000019700000003000004f30000003efc0100000002fb0000000800540069006d00650100000000000004f3000002eb00fffffffb0000000800540069006d0065010000000000000450000000000000000000000282000002b000000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1267
  X: 175
  Y: 204
