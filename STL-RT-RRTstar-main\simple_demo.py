#!/usr/bin/env python3
"""
STL-RT-RRT*简化演示脚本
展示如何使用和修改STL任务
"""

import sys
import time
import random
import dill
import os
from STLFormula import *

def create_simple_stl_tasks():
    """创建简单的STL任务示例"""
    
    print("=== 创建STL任务 ===")
    
    # 任务1: 房间参考系 - 访问特定区域
    print("1. 房间参考系任务：访问区域(150-200, 300-350)")
    visit_zone = STLPredicate2D(0, 1, 150, 200, 300, 350)
    task1 = Eventually(visit_zone, 20, 40)
    print(f"   STL公式: {task1}")
    
    # 任务2: 人类参考系 - 从右侧通过（小心翼翼）
    print("\n2. 人类参考系任务：从右侧小心通过")
    fragile_right = STLPredicate2D(0, 1, 80, 95, -60, 50)
    task2 = Untimed_Eventually(fragile_right)
    print(f"   STL公式: {task2}")
    print(f"   含义: 机器人最终必须进入人类右侧区域(80-95, -60-50)")
    
    # 任务3: 人类参考系 - 从左侧快速通过
    print("\n3. 人类参考系任务：从左侧快速通过")
    hurry_left = STLPredicate2D(0, 1, -80, -70, -100, 0)
    task3 = Eventually(hurry_left, 25, 35)
    print(f"   STL公式: {task3}")
    print(f"   含义: 机器人必须在25-35时间步内进入人类左侧区域(-80到-70, -100到0)")
    
    # 任务4: 复合任务 - 避开中央区域
    print("\n4. 复合任务：避开人类周围的安全区域")
    safe_zone = STLPredicate2D(0, 1, -30, 30, -30, 30)
    avoid_human = Negation(safe_zone)
    task4 = Always(avoid_human, 0, 50)
    print(f"   STL公式: {task4}")
    print(f"   含义: 在0-50时间步内始终避开人类周围30cm的安全区域")
    
    return {
        'visit_zone': task1,
        'fragile_right': task2, 
        'hurry_left': task3,
        'avoid_human': task4
    }

def test_stl_robustness():
    """测试STL鲁棒性计算"""
    
    print("\n=== 测试STL鲁棒性 ===")
    
    # 创建一个简单的2D区域约束
    zone = STLPredicate2D(0, 1, 80, 95, -60, 50)  # 右侧通过区域
    
    # 测试不同的轨迹点
    test_points = [
        [85, -30],   # 在区域内
        [100, -30],  # x坐标超出右边界
        [85, -70],   # y坐标超出下边界
        [70, -30],   # x坐标超出左边界
        [85, 60],    # y坐标超出上边界
    ]
    
    print("测试点的鲁棒性:")
    for i, point in enumerate(test_points):
        # 创建单点轨迹
        trajectory = [point]
        rho = zone.robustness(trajectory, 0)
        status = "满足约束" if rho > 0 else "违反约束"
        print(f"  点{i+1} {point}: 鲁棒性={rho:.2f} ({status})")
    
    return True

def demonstrate_task_modification():
    """演示如何修改STL任务"""
    
    print("\n=== 演示任务修改 ===")
    
    print("原始任务：从右侧通过")
    original_right = STLPredicate2D(0, 1, 80, 95, -60, 50)
    print(f"  区域: x∈[80,95], y∈[-60,50]")
    
    print("\n修改1：扩大通过区域（更宽松）")
    wider_right = STLPredicate2D(0, 1, 75, 100, -70, 60)
    print(f"  区域: x∈[75,100], y∈[-70,60]")
    
    print("\n修改2：缩小通过区域（更严格）")
    narrower_right = STLPredicate2D(0, 1, 85, 90, -50, 40)
    print(f"  区域: x∈[85,90], y∈[-50,40]")
    
    print("\n修改3：改变时间约束")
    print("  原始: Untimed_Eventually (任何时候)")
    print("  修改: Eventually(φ, 30, 40) (必须在30-40时间步内)")
    
    print("\n修改4：添加复合约束")
    print("  单一约束: Eventually(右侧通过)")
    print("  复合约束: Eventually(右侧通过) ∧ Always(避开危险区域)")
    
    # 演示如何创建复合约束
    right_pass = STLPredicate2D(0, 1, 80, 95, -60, 50)
    danger_zone = STLPredicate2D(0, 1, -20, 20, -20, 20)
    avoid_danger = Negation(danger_zone)
    
    compound_task = Conjunction([
        Eventually(right_pass, 30, 40),
        Always(avoid_danger, 0, 50)
    ])
    
    print(f"\n复合任务STL公式: {compound_task}")
    
    return True

def show_current_task_analysis():
    """分析当前项目中使用的STL任务"""
    
    print("\n=== 当前项目任务分析 ===")
    
    print("当前激活的任务（来自stl_rt_rrt_star.py main函数）:")
    
    # 重现当前使用的任务
    fragile_left = STLPredicate2D(0, 1, -95, -80, -150, -45)
    fragile_right = STLPredicate2D(0, 1, 80, 95, -60, 50)
    fragile = Disjunction([fragile_left, fragile_right])
    current_task = Untimed_Eventually(fragile)
    
    print(f"任务类型: 社交导航 - 小心翼翼通过")
    print(f"STL公式: {current_task}")
    print(f"具体含义:")
    print(f"  - 机器人最终必须从人类的左侧或右侧通过")
    print(f"  - 左侧区域: x∈[-95,-80], y∈[-150,-45]")
    print(f"  - 右侧区域: x∈[80,95], y∈[-60,50]")
    print(f"  - 这是'fragile'行为：给人类更大的空间")
    
    print(f"\n与其他社交行为的对比:")
    
    # hurry行为
    hurry_left = STLPredicate2D(0, 1, -80, -70, -100, 0)
    hurry_right = STLPredicate2D(0, 1, 60, 75, -60, 50)
    print(f"  hurry (急匆匆): 左侧[-80,-70]×[-100,0], 右侧[60,75]×[-60,50]")
    
    # walk行为  
    walk_left = STLPredicate2D(0, 1, -90, -80, -90, 0)
    walk_right = STLPredicate2D(0, 1, 70, 85, -60, 50)
    print(f"  walk (正常): 左侧[-90,-80]×[-90,0], 右侧[70,85]×[-60,50]")
    
    print(f"  fragile (小心): 左侧[-95,-80]×[-150,-45], 右侧[80,95]×[-60,50]")
    
    print(f"\n观察: fragile行为给人类留出了最大的空间，特别是左侧区域更远更低")
    
    return current_task

def create_custom_task_examples():
    """创建自定义任务示例"""
    
    print("\n=== 自定义任务示例 ===")
    
    print("示例1: 礼让行为")
    print("  机器人先等待人类通过，然后自己通过")
    
    # 定义中央通道
    central_channel = STLPredicate2D(0, 1, -20, 20, -50, 50)
    
    # 第一阶段：避开中央通道（让人类先过）
    wait_phase = Always(Negation(central_channel), 0, 30)
    
    # 第二阶段：通过中央通道
    pass_phase = Eventually(central_channel, 35, 45)
    
    polite_task = Conjunction([wait_phase, pass_phase])
    print(f"  STL公式: {polite_task}")
    
    print("\n示例2: 跟随行为")
    print("  机器人保持在人类后方一定距离")
    
    # 定义后方跟随区域
    follow_zone = STLPredicate2D(0, 1, -60, -40, -30, 30)
    follow_task = Always(follow_zone, 0, 100)
    print(f"  STL公式: {follow_task}")
    
    print("\n示例3: 引导行为")
    print("  机器人先到达目标区域，引导人类跟随")
    
    # 定义引导位置
    lead_zone = STLPredicate2D(0, 1, 40, 60, -20, 20)
    lead_task = Eventually(lead_zone, 10, 20)
    print(f"  STL公式: {lead_task}")
    
    return {
        'polite': polite_task,
        'follow': follow_task, 
        'lead': lead_task
    }

def main():
    """主演示函数"""
    
    print("STL-RT-RRT*任务演示和修改指南")
    print("=" * 60)
    
    try:
        # 1. 创建基本STL任务
        basic_tasks = create_simple_stl_tasks()
        
        # 2. 测试鲁棒性计算
        test_stl_robustness()
        
        # 3. 演示任务修改
        demonstrate_task_modification()
        
        # 4. 分析当前项目任务
        current_task = show_current_task_analysis()
        
        # 5. 创建自定义任务
        custom_tasks = create_custom_task_examples()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("\n如何修改STL任务:")
        print("1. 修改区域参数：调整STLPredicate2D的alpha,beta,gamma,delta值")
        print("2. 修改时间约束：调整Eventually/Always的t1,t2参数")
        print("3. 组合多个约束：使用Conjunction/Disjunction")
        print("4. 在stl_rt_rrt_star.py的main函数中替换specification变量")
        
        return True
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if success:
        print("\n✅ STL任务演示成功！现在您可以:")
        print("   - 修改stl_rt_rrt_star.py中的specification变量")
        print("   - 运行python stl_rt_rrt_star.py来测试新任务")
        print("   - 查看img/目录中生成的路径规划图片")
    else:
        print("\n❌ 演示失败，请检查环境配置。")
