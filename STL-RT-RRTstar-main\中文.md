# 用于社交机器人导航的实时STL-RRT\*

在STL（信号时序逻辑）规范下，用于机器人实时路径规划的代码。

-----

## 引言

在移动机器人的社交导航背景下，我们提出了一种在动态环境中进行实时路径规划的方法，该方法遵循以信号时序逻辑（STL）表达的时空约束。

STL规范封装了关于机器人在共享空间中应如何避开人类的偏好，其中人类在规划算法中被表示为动态障碍物。 我们从给定STL规范的轨迹的空间鲁棒性中推导出一个成本函数，并将其用于一个基于RRT\*的实时规划方法中。

我们提出的由该成本驱动的STL-RT-RRT\*算法保证了其运动规划（如果找到）能够渐进地最小化成本函数。 我们的结果表明，在社交适宜性方面，我们的方法优于基准的实时版RT-RRT\*。

-----

## 下载源码

您可以通过克隆此仓库来使用此API：

```
$ git clone https://github.com/KTH-RPL-Planiacs/STL-RT-RRTstar
```

依赖项：

  * Python 3.8
      * numpy
      * matplotlib
      * scipy
      * dill

-----

## STLFormula

`STLFormula.py` 模块实现了STL公式的形式化。 它支持布尔运算符（合取、析取、否定）和时序运算符（总是、最终）。

```python
from STLFormula import *
```

### 真与假布尔常量

```python
t = TrueF()
f = FalseF()
```

### 谓词 (Predicate)

`x_gt3 = Predicate(dimension,operator,mu,pi_index_signal)` 是一个STL公式，其构造函数接受4个参数：

  * `dimension`：维度的字符串/名称（例如：`'x'`）
  * `operator`：操作符 (`operatorclass.geq`, `operatorclass.lt`, `operatorclass.leq`, `operatorclass.gt`)
  * `mu`：浮点数 mu（例如：`3`）
  * `pi_index_signal`：在信号中，与该谓词维度对应的索引（例如：`0`）

### 二维时空谓词 (STLPredicate2D)

`zone1 = STLPredicate2D(index_dimension_x,index_dimension_y,alpha,beta,gamma,delta)` 是一个形式为 (α \< x \< β ∧ γ \< y \< δ) 的STL公式，其构造函数接受6个参数：

  * `index_signal_dimension_x`: x维度的维度索引（通常为0）
  * `index_signal_dimension_y`: y维度的维度索引（通常为1）
  * `alpha`: α
  * `beta`: β
  * `gamma`: γ
  * `delta`: δ

### 合取 (Conjunction) 与 析取 (Disjunction)

`c = Conjunction(phi1,phi2)` 和 `d = Disjunction(phi1,phi2)` 是STL公式，分别表示两个STL公式 `phi1` 和 `phi2` 的合取与析取。

### 否定 (Negation)

`n = Negation(phi)` 是一个STL公式，表示对一个STL公式 `phi` 的否定。

### 总是 (Always) 和 最终 (Eventually)

`a = Always(phi,t1,t2)` 和 `e = Eventually(phi,t1,t2)` 是STL公式，分别表示一个STL公式 `phi` 的“总是”和“最终”。它们都接受3个参数：

  * `phi`: 一个STL公式
  * `t1`: 时间区间的下界
  * `t2`: 时间区间的上界

### 无时间限制的“总是”和“最终”

`a = Untimed_Always(phi)` 和 `e = Untimed_Always(phi)` 是STL公式，分别表示一个STL公式 `phi` 的无时间限制的“总是”和“最终”。它们都接受1个参数：

  * `phi`: 一个STL公式

### 鲁棒性 (Robustness)

所有的STL公式都包含一个函数，用于根据给定的STL公式计算信号的鲁棒性。

```python
x_gt3 = Predicate('x',operatorclass.gt,3,0)
a = Always(x_gt3,0,5)
a.robustness([[3.1],[3.3],[3.2],[3.0],[2.9],[3.1],[3.5],[3.1],[2.2]],0)
-0.1
```

-----

## 实时RRTStar (Real-Time RRTStar)

`rt_rrt_star.py` 模块实现了RRT\*规划算法的实时版本（源自 Naderi 等人, 2015年的论文 "RT-RRT star: A Real-Time Path Planning Algorithm Based On RRT star"）。

```python
rrt_star = RRTStar(
    start=[50, 50],
    goal=[470, 390],
    rand_area=[50, 470, 50, 390],
    obstacle_list=[],
    expand_dis=10,
    max_iter=10000,
    max_time=0.1,
    goal_sample_rate=5,
    path_resolution=10,
    grid_size=20,
    warm_start=False,
    warm_start_tree_size=1000,
    robot_radius=30)
```

RRTStar对象的构造函数接受多个参数，其中包括：

  * `start` 和 `goal`: 二维的起点和终点坐标。
  * `rand_area`: 形式为 `[x_min, x_max, y_min, y_max]`，定义了采样的二维区域范围。
  * `warm_start`: 是否开启离线构建树。如果为是，则还需设置 `warm_start_tree_size`：在上线使用前，离线生成树的最大规模。

`rrt_star.set_new_start_new_goal(new_start,new_goal)`
该函数为树设置新的起点（即新的树根）和新的终点，并从根节点重连（rewire）现有的树。

`path, path_nodes = rrt_star.planning(current_pos=current_pos,updated_obstacle_list=updated_obstacle_list)`
此函数用于实时规划，以给定的频率被调用来更新规划。`current_pos` 是代理（机器人）当前测量到的位置，`updated_obstacle_list` 是更新后的待避开障碍物列表。 函数返回 `path`（从代理当前位置到目标的二维位置列表）和 `path_nodes`（RRT\*树中从代理当前位置到目标的节点列表）。

-----

## 带STL约束的实时RRTStar (STL Real-Time RRTStar)

`stl_rt_rrt_star.py` 模块实现了带STL约束的实时RRT\*规划算法。

```python
rrt_star = RRTStar(
    start=[50, 50],
    goal=[470, 390],
    rand_area=[50, 470, 50, 390],
    obstacle_list=[],
    expand_dis=10,
    max_iter=10000,
    max_time=0.1,
    goal_sample_rate=5,
    path_resolution=10,
    grid_size=20,
    warm_start=False,
    warm_start_tree_size=1000,
    robot_radius=30)
```

RRTStar对象的构造函数接受多个参数，其中包括：

  * `start` 和 `goal`: 二维的起点和终点坐标。
  * `rand_area`: 形式为 `[x_min, x_max, y_min, y_max]`，定义了采样的二维区域范围。
  * `warm_start`: 是否开启离线构建树。如果为是，则还需设置 `warm_start_tree_size`：在上线使用前，离线生成树的最大规模。

`rrt_star.set_new_start_new_goal(new_start,new_goal,specification)`
该函数为树设置新的起点（即新的树根）和新的终点，并从根节点重连现有的树。`specification` 是一个使用 `STLFormula.py` 模块定义的STL规范。

`(path, path_nodes), _, _ = rrt_star.planning(current_pos=current_pos,previous_human_position=previous_human_position,updated_human_position=updated_human_position,stl_specification=specification)`
此函数专为在STL约束下的人机交互场景规划而设计。它被实时调用以更新规划。`current_pos` 是代理当前测量到的位置，`previous_human_position` 是上一次人类的位置，`updated_human_position` 是更新后的人类位置，`specification` 是一个使用`STLFormula.py`模块定义的STL规范。 函数返回 `path`（从代理当前位置到目标的二维位置列表）和 `path_nodes`（RRT\*树中从代理当前位置到目标的节点列表）。