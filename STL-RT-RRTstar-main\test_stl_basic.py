#!/usr/bin/env python3
"""
STL-RT-RRT*基础功能测试（无可视化）
用于验证核心算法功能
"""

import sys
import time
import random
from STLFormula import *

def test_stl_formulas():
    """测试STL公式的基本功能"""
    print("=== 测试STL公式 ===")
    
    # 测试基础谓词
    print("1. 测试基础谓词")
    x_gt_100 = Predicate('x', operatorclass.gt, 100, 0)
    y_lt_200 = Predicate('y', operatorclass.lt, 200, 1)
    
    # 测试信号
    test_signal = [[150, 180], [120, 190], [80, 210]]
    
    for t in range(len(test_signal)):
        rho_x = x_gt_100.robustness(test_signal, t)
        rho_y = y_lt_200.robustness(test_signal, t)
        print(f"  时间{t}: 信号{test_signal[t]}, x>100鲁棒性={rho_x:.2f}, y<200鲁棒性={rho_y:.2f}")
    
    # 测试2D谓词
    print("\n2. 测试2D空间谓词")
    zone = STLPredicate2D(0, 1, 100, 200, 150, 250)  # 100<x<200 且 150<y<250
    
    for t in range(len(test_signal)):
        rho = zone.robustness(test_signal, t)
        print(f"  时间{t}: 信号{test_signal[t]}, 在区域内鲁棒性={rho:.2f}")
    
    # 测试时序算子
    print("\n3. 测试时序算子")
    eventually_zone = Eventually(zone, 0, 2)
    always_zone = Always(zone, 0, 2)
    
    rho_eventually = eventually_zone.robustness(test_signal, 0)
    rho_always = always_zone.robustness(test_signal, 0)
    
    print(f"  Eventually(zone, 0, 2)鲁棒性: {rho_eventually:.2f}")
    print(f"  Always(zone, 0, 2)鲁棒性: {rho_always:.2f}")
    
    return True

def test_social_navigation_stl():
    """测试社交导航STL规范"""
    print("\n=== 测试社交导航STL规范 ===")
    
    # 定义人类参考系中的通过区域
    hurry_left = STLPredicate2D(0, 1, -80, -70, -100, 0)
    hurry_right = STLPredicate2D(0, 1, 60, 75, -60, 50)
    hurry_zones = Disjunction([hurry_left, hurry_right])
    
    walk_left = STLPredicate2D(0, 1, -90, -80, -90, 0)
    walk_right = STLPredicate2D(0, 1, 70, 85, -60, 50)
    walk_zones = Disjunction([walk_left, walk_right])
    
    fragile_left = STLPredicate2D(0, 1, -95, -80, -150, -45)
    fragile_right = STLPredicate2D(0, 1, 80, 95, -60, 50)
    fragile_zones = Disjunction([fragile_left, fragile_right])
    
    # 创建时序规范
    hurry_spec = Eventually(hurry_zones, 25, 35)
    walk_spec = Eventually(walk_zones, 30, 40)
    fragile_spec = Untimed_Eventually(fragile_zones)
    
    # 模拟机器人轨迹（在人类参考系中）
    trajectories = {
        'left_pass': [[-75, -50], [-85, -30], [-90, -10], [-85, 10]],  # 从左侧通过
        'right_pass': [[65, -30], [70, -10], [85, 10], [90, 30]],      # 从右侧通过
        'center_block': [[0, -10], [5, 0], [0, 10], [-5, 0]],          # 阻挡中央
        'far_left': [[-100, -100], [-110, -80], [-120, -60]],          # 远离左侧
    }
    
    specs = {
        'hurry': hurry_spec,
        'walk': walk_spec,
        'fragile': fragile_spec
    }
    
    print("轨迹鲁棒性分析:")
    for traj_name, trajectory in trajectories.items():
        print(f"\n轨迹: {traj_name}")
        for spec_name, spec in specs.items():
            try:
                rho = spec.robustness(trajectory, 0)
                status = "满足" if rho > 0 else "违反"
                print(f"  {spec_name}规范: 鲁棒性={rho:.2f} ({status})")
            except Exception as e:
                print(f"  {spec_name}规范: 计算错误 - {e}")
    
    return True

def test_stl_cost_calculation():
    """测试STL成本计算"""
    print("\n=== 测试STL成本计算 ===")
    
    # 创建简单的STL规范
    zone = STLPredicate2D(0, 1, 80, 95, -60, 50)  # 右侧通过区域
    spec = Untimed_Eventually(zone)
    
    # 模拟节点轨迹
    class MockNode:
        def __init__(self, x, y):
            self.x = x
            self.y = y
            self.trajectory_until_node = []
            self.parent = None
            self.stl_cost = 0.0
            self.rho_bar = float('-inf')
    
    # 创建轨迹节点
    nodes = [
        MockNode(50, 50),    # 起点
        MockNode(60, 40),    # 中间点1
        MockNode(85, 20),    # 进入目标区域
        MockNode(90, 10),    # 目标区域内
    ]
    
    # 构建轨迹
    for i in range(len(nodes)):
        nodes[i].trajectory_until_node = nodes[:i+1]
        if i > 0:
            nodes[i].parent = nodes[i-1]
    
    # 计算STL成本
    print("节点STL成本计算:")
    for i, node in enumerate(nodes):
        try:
            if i == 0:
                spec.stl_rrt_cost_function_root_node(node, None)
            else:
                spec.stl_rrt_cost_function(node)
            print(f"  节点{i} ({node.x}, {node.y}): STL成本={node.stl_cost:.3f}, 鲁棒性={node.rho_bar:.3f}")
        except Exception as e:
            print(f"  节点{i}: 计算错误 - {e}")
    
    return True

def main():
    """主测试函数"""
    print("STL-RT-RRT*基础功能测试")
    print("=" * 50)
    
    try:
        # 测试STL公式
        test_stl_formulas()
        
        # 测试社交导航STL
        test_social_navigation_stl()
        
        # 测试STL成本计算
        test_stl_cost_calculation()
        
        print("\n" + "=" * 50)
        print("所有测试完成！STL-RT-RRT*核心功能正常。")
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    if success:
        print("\n✅ 基础功能测试通过，可以继续进行完整的STL-RT-RRT*实验。")
    else:
        print("\n❌ 基础功能测试失败，请检查环境配置。")
