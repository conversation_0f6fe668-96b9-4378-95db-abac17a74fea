import numpy
import matplotlib.pyplot as plt
import random
import math

# Returns theta in [-pi/2, 3pi/2]
def generate_theta(a, b):
    """
    生成一个在 [-pi/2, 3pi/2] 范围内的随机角度 theta。

    参数:
    a (float): 椭圆的长半轴长度。
    b (float): 椭圆的短半轴长度。

    返回:
    float: 随机生成的角度 theta，范围在 [-pi/2, 3pi/2] 内。
    """
    # 生成一个 [0, 0.25) 范围内的随机数
    u = random.random() / 4.0
    # 根据 u 计算初始角度 theta
    theta = numpy.arctan(b/a * numpy.tan(2*numpy.pi*u))

    # 生成一个 [0, 1) 范围内的随机数
    v = random.random()
    if v < 0.25:
        # 25% 的概率返回初始 theta
        return theta
    elif v < 0.5:
        # 25% 的概率返回 pi - theta
        return numpy.pi - theta
    elif v < 0.75:
        # 25% 的概率返回 pi + theta
        return numpy.pi + theta
    else:
        # 25% 的概率返回 -theta
        return -theta

def radius(a, b, theta):
    """
    计算椭圆上给定角度 theta 处的半径。

    参数:
    a (float): 椭圆的长半轴长度。
    b (float): 椭圆的短半轴长度。
    theta (float): 角度，以弧度为单位。

    返回:
    float: 椭圆上该角度处的半径。
    """
    return a * b / numpy.sqrt((b*numpy.cos(theta))**2 + (a*numpy.sin(theta))**2)

def random_point(a, b):
    """
    在椭圆内生成一个随机点。

    参数:
    a (float): 椭圆的长半轴长度。
    b (float): 椭圆的短半轴长度。

    返回:
    numpy.ndarray: 包含随机点的 x 和 y 坐标的数组。
    """
    # 生成一个随机角度
    random_theta = generate_theta(a, b)
    # 计算该角度下椭圆的最大半径
    max_radius = radius(a, b, random_theta)
    # 生成一个小于最大半径的随机半径
    random_radius = max_radius * numpy.sqrt(random.random())

    return numpy.array([
        # 计算随机点的 x 坐标
        random_radius * numpy.cos(random_theta),
        # 计算随机点的 y 坐标
        random_radius * numpy.sin(random_theta)
    ])

def rotate(origin, point, angle):
    """
    将一个点绕给定原点逆时针旋转给定角度。

    参数:
    origin (tuple): 旋转原点的 (x, y) 坐标。
    point (tuple): 需要旋转的点的 (x, y) 坐标。
    angle (float): 旋转角度，以弧度为单位。

    返回:
    tuple: 旋转后点的 (x, y) 坐标。
    """
    ox, oy = origin
    px, py = point

    # 计算旋转后点的 x 坐标
    qx = ox + math.cos(angle) * (px - ox) - math.sin(angle) * (py - oy)
    # 计算旋转后点的 y 坐标
    qy = oy + math.sin(angle) * (px - ox) + math.cos(angle) * (py - oy)
    return qx, qy
    
def get_random_node_ellipsis_sampling(start=[50,50],goal=[470,390]):
    """
    使用椭圆采样方法生成一个随机节点。

    参数:
    start (list, optional): 起点的 [x, y] 坐标，默认为 [50, 50]。
    goal (list, optional): 终点的 [x, y] 坐标，默认为 [470, 390]。

    返回:
    tuple: 随机节点的 (x, y) 坐标。
    """
    # 计算起点和终点在 x 轴上的距离
    dx = goal[0] - start[0]
    # 计算起点和终点在 y 轴上的距离
    dy = goal[1] - start[1]
    # 计算起点和终点之间的直线距离
    d = math.hypot(dx, dy)
    # 计算起点到终点的角度
    theta = math.atan2(dy, dx)

    # 椭圆的参数：a 是椭圆的水平半径（等于起点和终点之间距离的一半）
    a = d/2
    # b 是椭圆的垂直半径（硬编码为 a 的一半，可作为参数）
    b = d/4
    
    # 生成一个随机角度
    random_theta = generate_theta(a, b)
    # 计算该角度下椭圆的最大半径
    max_radius = radius(a, b, random_theta)
    # 生成一个小于最大半径的随机半径
    random_radius = max_radius * numpy.sqrt(random.random())

    # 计算随机点的 x 坐标
    x = random_radius * numpy.cos(random_theta)
    # 计算随机点的 y 坐标
    y = random_radius * numpy.sin(random_theta)
    # 将随机点绕指定原点旋转指定角度
    rx, ry = rotate((-a,0), (x,y), theta)
    # 返回随机节点的最终坐标
    return rx+(d/2)+start[0], ry+start[1]


# points = numpy.array([random_point(a, b) for _ in range(2000)])
# rotated_points = []
# for point in points:
    # x = point[0]
    # y = point[1]
    # rx, ry = rotate((-a,0), (x,y), theta)
    # rotated_points.append(numpy.array([rx+(d/2)+start[0], ry+start[1]]))
# rotated_points=numpy.array(rotated_points)rotated_points = []
# for point in points:
    # x = point[0]
    # y = point[1]
    # rx, ry = rotate((-a,0), (x,y), theta)
    # rotated_points.append(numpy.array([rx+(d/2)+start[0], ry+start[1]]))
# rotated_points=numpy.array(rotated_points)

# room_area=[0,520,0,440]
# plt.xlim(room_area[:2])
# plt.ylim(room_area[2:])  
# plt.scatter(rotated_points[:,0], rotated_points[:,1])
# plt.show()
