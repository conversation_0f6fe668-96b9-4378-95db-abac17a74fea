<launch>
    <!-- move base -->
    <node pkg="move_base" type="move_base" respawn="false" name="move_base" output="screen" clear_params="true">

        <param name="base_gocal_planner" value="global_planner/GlobalPlanner"/>

        <rosparam file="$(find HPHS)/param/costmap_common_params.yaml" command="load" ns="global_costmap" />
        <rosparam file="$(find HPHS)/param/costmap_common_params.yaml" command="load" ns="local_costmap" />
        <rosparam file="$(find HPHS)/param/local_costmap_params.yaml" command="load" />
        <rosparam file="$(find HPHS)/param/global_costmap_params.yaml" command="load" />
        <rosparam file="$(find HPHS)/param/base_local_planner_params.yaml" command="load" />
        <rosparam file="$(find HPHS)/param/base_global_planner_params.yaml" command="load" />

        <remap from="cmd_vel" to="/0/cmd_vel"/> <!--Note that the topic name should be different from the actual topic,
                                                    since the speed of the robot is controlled by the cmu planner-->
        <remap from="odom" to="/state_estimation"/>
    </node>
</launch>
