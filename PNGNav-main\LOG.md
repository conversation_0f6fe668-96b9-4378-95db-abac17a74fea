# LOG

## 240817
1. Added ICRA paper link and updated citation in `README.md`.

## 240503
1. Cleaned `local_planner` files.
```
src/png_navigation/scripts/local_planner_node.py
src/png_navigation/scripts_dynamic_obstacles/local_planner_node_check.py
```
2. Updated `How to Create Your Own Map Yaml File` in `README.md` by adding the github repo [ros_map_editor](https://github.com/TheOnceAndFutureSmalltalker/ros_map_editor).
3. Added `Real World Deployment on TurtleBot 2i` in `README.md`. Added the github repo[CrowdNav_Sim2Real_Turtlebot](https://github.com/Shuijing725/CrowdNav_Sim2Real_Turtlebot).
4. Tested all changes. Release v1.0.1.

## 240422
1. Removed irrelevant files.
```
src/png_navigation/src/png_navigation/datasets/planning_problem_utils.py
src/png_navigation/src/png_navigation/datasets/planning_problem_utils_2d.py
src/png_navigation/src/png_navigation/datasets/point_cloud_mask_utils.py
src/png_navigation/src/png_navigation/datasets/rectangle_env_2d.py
src/png_navigation/src/png_navigation/datasets/rectangle_point_cloud_2d.py
src/png_navigation/src/png_navigation/path_planning_classes/nirrt_star_png_c_2d.py
src/png_navigation/src/png_navigation/path_planning_classes/rrt_env_v1.py
```
2. Updated links for YouTube. Main GitHub repo is released.

## 240308
1. Update ICRA 2024 paper/author information.
   
## 240229
1. Add links to train/eval GitHub repo and Google project website. 

## 240204
1. PNGNav and `png_navigation` v1.0.0 are released.

## 240203
1. The public repo PNGNav is created.