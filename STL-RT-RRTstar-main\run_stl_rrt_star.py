#!/usr/bin/env python3
"""
简化的STL-RT-RRT*运行脚本
用于快速复现和测试不同的STL任务
"""

import sys
import os
import time
import random
import dill
import matplotlib.pyplot as plt
from STLFormula import *
from stl_rt_rrt_star import RRTStar

# 确保img目录存在
if not os.path.exists('img'):
    os.makedirs('img')

def create_stl_specifications():
    """创建各种STL规范"""
    
    # 1. 房间参考系任务
    alpha, beta, gamma, delta = 150, 200, 300, 350
    room_zone = STLPredicate2D(0, 1, alpha, beta, gamma, delta)
    room_eventually = Eventually(room_zone, 20, 40)
    room_always = Always(room_zone, 20, 40)
    
    # 2. 人类参考系任务 - hurry（急匆匆）
    hurry_left = STLPredicate2D(0, 1, -80, -70, -100, 0)
    hurry_right = STLPredicate2D(0, 1, 60, 75, -60, 50)
    hurry = Disjunction([hurry_left, hurry_right])
    hurry_timed = Eventually(hurry, 25, 35)
    hurry_untimed = Untimed_Eventually(hurry)
    
    # 3. 人类参考系任务 - walk（正常行走）
    walk_left = STLPredicate2D(0, 1, -90, -80, -90, 0)
    walk_right = STLPredicate2D(0, 1, 70, 85, -60, 50)
    walk = Disjunction([walk_left, walk_right])
    walk_timed = Eventually(walk, 30, 40)
    walk_untimed = Untimed_Eventually(walk)
    
    # 4. 人类参考系任务 - fragile（小心翼翼）
    fragile_left = STLPredicate2D(0, 1, -95, -80, -150, -45)
    fragile_right = STLPredicate2D(0, 1, 80, 95, -60, 50)
    fragile = Disjunction([fragile_left, fragile_right])
    fragile_timed = Eventually(fragile, 35, 45)
    fragile_untimed = Untimed_Eventually(fragile)
    
    return {
        'room_eventually': room_eventually,
        'room_always': room_always,
        'hurry_timed': hurry_timed,
        'hurry_untimed': hurry_untimed,
        'walk_timed': walk_timed,
        'walk_untimed': walk_untimed,
        'fragile_timed': fragile_timed,
        'fragile_untimed': fragile_untimed
    }

def create_dynamic_environment():
    """创建动态环境（人类移动轨迹）"""
    environment = {}
    radius_obs = 20
    
    # 人类从(470,390)移动到(50,50)的轨迹
    start_x, start_y = 470.0, 390.0
    end_x, end_y = 50.0, 50.0
    
    # 前0.5秒静止
    for i in range(6):  # 0.0 to 0.5
        t = i * 0.1
        environment[t] = [(start_x, start_y, radius_obs)]
    
    # 接下来5秒线性移动
    for i in range(50):  # 0.6 to 5.5
        t = 0.6 + i * 0.1
        progress = i / 49.0
        x = start_x + (end_x - start_x) * progress
        y = start_y + (end_y - start_y) * progress
        environment[t] = [(x, y, radius_obs)]
    
    # 最后2秒在终点静止
    for i in range(20):  # 5.6 to 7.5
        t = 5.6 + i * 0.1
        environment[t] = [(end_x, end_y, radius_obs)]
    
    return environment

def run_stl_rrt_star(specification_name='fragile_untimed', use_precomputed=True):
    """运行STL-RT-RRT*算法"""
    
    print(f"运行STL-RT-RRT*算法，使用规范: {specification_name}")
    
    # 创建STL规范
    specifications = create_stl_specifications()
    if specification_name not in specifications:
        print(f"错误：未知的规范名称 {specification_name}")
        return
    
    specification = specifications[specification_name]
    print(f"STL规范: {specification}")
    
    # 创建动态环境
    environment = create_dynamic_environment()
    
    # 初始化RRT*
    if use_precomputed and os.path.exists("stl_specifications/stl_tree_200_1500_rewired_fragile_right.dill"):
        print("加载预计算的树...")
        rrt_star = dill.load(open("stl_specifications/stl_tree_200_1500_rewired_fragile_right.dill", "rb"))
        rrt_star.human_referential = True
    else:
        print("创建新的RRT*树...")
        rrt_star = RRTStar(
            start=[50, 50],
            goal=[470, 390],
            rand_area=[50, 470, 50, 390],
            obstacle_list=[],
            expand_dis=20,
            max_iter=10000,
            max_time=0.1,
            goal_sample_rate=5,
            path_resolution=20,
            grid_size=20,
            warm_start=True,
            warm_start_tree_size=1500,
            robot_radius=24,
            human_referential=True
        )
    
    # 运行实时规划
    followed_path = []
    previous_human_position = [478.4, 396.8]
    
    for t in sorted(environment.keys()):
        if t > 3.0:  # 限制运行时间用于演示
            break
            
        print(f"\n时间步 t={t:.1f}")
        
        # 获取当前机器人位置（简化处理）
        try:
            current_pos = rrt_star.start if t == 0.0 else path_nodes[1] if len(path_nodes) > 1 else path_nodes[0]
        except:
            current_pos = rrt_star.start
        
        # 执行规划
        start_time = time.time()
        (path, path_nodes), cost_time, rewire_checks = rrt_star.planning(
            animation=False,
            current_pos=current_pos,
            previous_human_position=previous_human_position,
            updated_human_position=environment[t][0][:2],  # 只取x,y坐标
            stl_specification=specification
        )
        elapsed = time.time() - start_time
        
        path.reverse()
        path_nodes.reverse()
        
        print(f"规划完成，耗时: {elapsed:.3f}秒")
        print(f"路径长度: {len(path)}")
        
        # 记录路径
        if len(path_nodes) > 0:
            if hasattr(path_nodes[0], 'x_humanreferential'):
                followed_path.append((path_nodes[0].x_humanreferential, path_nodes[0].y_humanreferential))
        
        # 可视化（每隔几步保存一次图片）
        if int(t * 10) % 5 == 0:  # 每0.5秒保存一次
            rrt_star.draw_graph(room_area=[0, 520, 0, 440])
            plt.plot([x for (x, y) in path], [y for (x, y) in path], 'r--', linewidth=2)
            plt.title(f'STL-RT-RRT* at t={t:.1f}s, Spec: {specification_name}')
            plt.grid(True)
            plt.savefig(f'img/{t:.1f}_stl_{specification_name}.png', dpi=150, bbox_inches='tight')
            plt.close()
        
        previous_human_position = environment[t][0][:2]
    
    print(f"\n完成！跟踪的路径: {followed_path}")
    return followed_path

if __name__ == '__main__':
    # 可以修改这里来测试不同的STL规范
    specification_to_test = 'fragile_untimed'  # 可选: fragile_untimed, hurry_untimed, walk_untimed等
    
    try:
        path = run_stl_rrt_star(specification_to_test)
        print("STL-RT-RRT*运行成功！")
    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()
