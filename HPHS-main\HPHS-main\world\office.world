<sdf version='1.7'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
      <spot>
        <inner_angle>0</inner_angle>
        <outer_angle>0</outer_angle>
        <falloff>0</falloff>
      </spot>
    </light>
    <physics name='default_physics' default='0' type='ode'>
      <real_time_update_rate>1000</real_time_update_rate>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <ode>
        <solver>
          <type>quick</type>
          <iters>150</iters>
          <precon_iters>0</precon_iters>
          <sor>1.4</sor>
          <use_dynamic_moi_rescaling>1</use_dynamic_moi_rescaling>
        </solver>
        <constraints>
          <cfm>1e-05</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>2000</contact_max_correcting_vel>
          <contact_surface_layer>0.01</contact_surface_layer>
        </constraints>
      </ode>
    </physics>
    <model name='box_1'>
      <pose>0.05 -12.05 1.25 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>16.1 0.1 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>16.1 0.1 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/GreyTransparent</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_2'>
      <pose>8.05 0.05 1.25 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 24.1 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 24.1 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/GreyTransparent</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_3'>
      <pose>-0.05 12.05 1.25 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>16.1 0.1 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>16.1 0.1 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/GreyTransparent</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_4'>
      <pose>-8.05 -0.05 1.25 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 24.1 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 24.1 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/GreyTransparent</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_5'>
      <pose>0 0 -0.05 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>16.2 24.2 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>16.2 24.2 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/WoodFloor</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <gravity>0 0 -9.8066</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <wind/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='box_9_clone_clone_0'>
      <pose>-3.46298 -8.44795 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_1'>
      <pose>-3.40186 -7.56359 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_2'>
      <pose>-3.23885 -6.64819 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <state world_name='default'>
      <sim_time>6435 658000000</sim_time>
      <real_time>38 279623999</real_time>
      <wall_time>1720073137 749282763</wall_time>
      <iterations>36047</iterations>
      <model name='box_1'>
        <pose>0.05 -12.05 1.25 0 -0 0</pose>
        <scale>1.70705 1 1</scale>
        <link name='link'>
          <pose>0.05 -12.05 1.25 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_2'>
        <pose>13.6164 0.05 1.25 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>13.6164 0.05 1.25 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_3'>
        <pose>-0.05 12.05 1.25 0 -0 0</pose>
        <scale>1.70545 1 1</scale>
        <link name='link'>
          <pose>-0.05 12.05 1.25 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_4'>
        <pose>-13.7117 -0.05 1.25 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-13.7117 -0.05 1.25 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_5'>
        <pose>-0.057129 0.081047 -0.05 0 -0 0</pose>
        <scale>1.68805 1 1</scale>
        <link name='link'>
          <pose>-0.057129 0.081047 -0.05 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_1_clone'>
        <pose>9.63242 -8.01755 0.74041 0 0 -1.19602</pose>
        <scale>1 0.420417 0.596456</scale>
        <link name='link'>
          <pose>9.63242 -8.01755 0.74041 0 0 -1.19602</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_1_clone_0'>
        <pose>8.61855 1.50825 0.618044 0 -0 1.16323</pose>
        <scale>1 1.66172 0.46394</scale>
        <link name='link'>
          <pose>8.61855 1.50825 0.618044 0 -0 1.16323</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone'>
        <pose>4.19641 4.36405 1.25 0 -0 1.5473</pose>
        <scale>1 0.832456 1</scale>
        <link name='link'>
          <pose>4.19641 4.36405 1.25 0 -0 1.5473</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone_clone'>
        <pose>4.24691 3.74326 1.25 0 -0 1.5473</pose>
        <scale>1 0.801809 1</scale>
        <link name='link'>
          <pose>4.24691 3.74326 1.25 0 -0 1.5473</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone_clone_0'>
        <pose>4.18218 3.00755 1.25 0 -0 1.5473</pose>
        <scale>1 0.816941 1</scale>
        <link name='link'>
          <pose>4.18218 3.00755 1.25 0 -0 1.5473</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone_clone_1'>
        <pose>4.8146 3.66742 1.25 0 0 -0.001309</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>4.8146 3.66742 1.25 0 0 -0.001309</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone_clone_1_clone'>
        <pose>-8.92093 -3.32073 1.25 0 0 -0.001309</pose>
        <scale>4.07547 1.23568 1</scale>
        <link name='link'>
          <pose>-8.92093 -3.32073 1.25 0 0 -0.001309</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone_clone_clone'>
        <pose>9.37461 0.934459 1.25 0 -0 1.56028</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>9.37461 0.934459 1.25 0 -0 1.56028</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone_clone_clone_0'>
        <pose>9.4532 1.11788 1.25 0 -0 1.57422</pose>
        <scale>1 2.01481 1</scale>
        <link name='link'>
          <pose>9.4532 1.11788 1.25 0 -0 1.57422</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone_clone_clone_clone'>
        <pose>9.36555 0.57412 1.25 0 -0 3.12409</pose>
        <scale>1 0.463542 1</scale>
        <link name='link'>
          <pose>9.36555 0.57412 1.25 0 -0 3.12409</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone_clone_clone_clone_clone_0'>
        <pose>10.5612 -7.26456 0.728231 0 -0 3.12409</pose>
        <scale>1 1 0.587201</scale>
        <link name='link'>
          <pose>10.5612 -7.26456 0.728231 0 -0 3.12409</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_4_clone_clone_clone_clone_clone_0_clone'>
        <pose>8.67421 -8.77015 0.728231 0 -0 3.12409</pose>
        <scale>1 1.11155 1</scale>
        <link name='link'>
          <pose>8.67421 -8.77015 0.728231 0 -0 3.12409</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0'>
        <pose>-5.51729 9.19762 1.25 0 0 -2.41059</pose>
        <scale>1 0.429368 1</scale>
        <link name='link'>
          <pose>-5.51729 9.19762 1.25 0 0 -2.41059</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone'>
        <pose>-4.45373 9.15321 1.25 0 -0 2.3647</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-4.45373 9.15321 1.25 0 -0 2.3647</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_0'>
        <pose>-3.36619 9.12539 1.25 0 0 -2.41059</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-3.36619 9.12539 1.25 0 0 -2.41059</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_0_clone'>
        <pose>10.2349 7.47203 1.25 0 0 -2.41059</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>10.2349 7.47203 1.25 0 0 -2.41059</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_1'>
        <pose>-3.90632 10.5012 1.25 0 0 -3.13387</pose>
        <scale>10.7348 1.7327 1</scale>
        <link name='link'>
          <pose>-3.90632 10.5012 1.25 0 0 -3.13387</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_2'>
        <pose>8.12867 7.55946 1.25 0 0 -2.41059</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>8.12867 7.55946 1.25 0 0 -2.41059</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone'>
        <pose>-12.9412 -8.2324 1.25 0 0 -1.91939</pose>
        <scale>1 1.3317 1</scale>
        <link name='link'>
          <pose>-12.9412 -8.2324 1.25 0 0 -1.91939</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_0'>
        <pose>10.0891 10.8411 1.25 0 0 -2.41059</pose>
        <scale>1 2.02316 1</scale>
        <link name='link'>
          <pose>10.0891 10.8411 1.25 0 0 -2.41059</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_clone'>
        <pose>-13.0267 -9.02916 1.25 0 0 -1.13074</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-13.0267 -9.02916 1.25 0 0 -1.13074</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_clone_0'>
        <pose>-12.6684 9.96416 1.25 0.004899 -0 -2.17636</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-12.6684 9.96416 1.25 0.004899 -0 -2.17636</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_clone_0_clone'>
        <pose>-10.9454 9.83136 1.25 0.001692 -0.004598 -0.95822</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-10.9454 9.83136 1.25 0.001692 -0.004598 -0.95822</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_clone_clone'>
        <pose>-10.5404 11.4399 1.25 0 -0 0.330253</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-10.5404 11.4399 1.25 0 -0 0.330253</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_clone'>
        <pose>-2.34285 9.09006 1.25 0 -0 2.3647</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-2.34285 9.09006 1.25 0 -0 2.3647</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_6_clone_clone_clone_0_clone_0_clone_clone_clone'>
        <pose>9.16663 7.48045 1.25 0 -0 2.3647</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>9.16663 7.48045 1.25 0 -0 2.3647</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_8_clone_clone_4_clone_1'>
        <pose>0.92481 2.34897 0.68407 0 -0 0.698352</pose>
        <scale>0.884011 1 0.670591</scale>
        <link name='link'>
          <pose>0.92481 2.34897 0.68407 0 -0 0.698352</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_8_clone_clone_4_clone_1_clone'>
        <pose>0.020285 3.21878 0.598648 0 -0 0.698352</pose>
        <scale>1 1 0.726621</scale>
        <link name='link'>
          <pose>0.020285 3.21878 0.598648 0 -0 0.698352</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_8_clone_clone_4_clone_2_clone'>
        <pose>-3.89284 -6.16269 1.25 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-3.89284 -6.16269 1.25 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_0'>
        <pose>-3.46298 -8.44795 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-3.46298 -8.44795 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_1'>
        <pose>-3.40186 -7.56359 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-3.40186 -7.56359 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_2'>
        <pose>-3.23885 -6.64819 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-3.23885 -6.64819 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2'>
        <pose>4.51157 6.4869 0.75 0 -0 0</pose>
        <scale>0.217022 4.27843 1</scale>
        <link name='link'>
          <pose>4.51157 6.4869 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone'>
        <pose>-8.20929 5.84781 0.75 0 -0 0</pose>
        <scale>1.93095 1.25376 1</scale>
        <link name='link'>
          <pose>-8.20929 5.84781 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone'>
        <pose>1.29918 -0.998345 0.75 0 -0 1.56359</pose>
        <scale>0.461579 0.552818 1</scale>
        <link name='link'>
          <pose>1.29918 -0.998345 0.75 0 -0 1.56359</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone'>
        <pose>1.05576 -11.8471 0.75 0 -0 1.56352</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>1.05576 -11.8471 0.75 0 -0 1.56352</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_0'>
        <pose>-11.4677 -0.34019 0.75 0 -0 1.56359</pose>
        <scale>1 0.604599 1</scale>
        <link name='link'>
          <pose>-11.4677 -0.34019 0.75 0 -0 1.56359</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_0_clone'>
        <pose>4.9281 -7.96233 0.75 0 0 -2.33603</pose>
        <scale>1 0.611988 1</scale>
        <link name='link'>
          <pose>4.9281 -7.96233 0.75 0 -0 -2.33603</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_0_clone_clone'>
        <pose>4.59014 -2.84305 0.75 0 -0 3.14009</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>4.59014 -2.84305 0.75 0 -0 3.14009</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone'>
        <pose>9.54444 -6.45224 0.75 0 -0 1.56352</pose>
        <scale>1.6298 0.999712 1</scale>
        <link name='link'>
          <pose>9.54444 -6.45224 0.75 0 -0 1.56352</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone'>
        <pose>9.54266 -2.76485 0.75 0 -0 1.56352</pose>
        <scale>1.77455 1 1</scale>
        <link name='link'>
          <pose>9.54266 -2.76485 0.75 0 -0 1.56352</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_0'>
        <pose>9.53139 -9.54857 0.75 0 -0 1.56352</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>9.53139 -9.54857 0.75 0 -0 1.56352</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_0_clone'>
        <pose>-13.5014 -9.06923 0.75 0 -0 1.56352</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-13.5014 -9.06923 0.75 0 -0 1.56352</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_1'>
        <pose>8.29292 2.40961 0.75 0 0 -3.13353</pose>
        <scale>1 0.592171 1</scale>
        <link name='link'>
          <pose>8.29292 2.40961 0.75 0 0 -3.13353</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_1_clone'>
        <pose>10.7143 2.50136 0.75 0 0 -3.13353</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>10.7143 2.50136 0.75 0 0 -3.13353</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_1_clone_clone'>
        <pose>1.38751 -10.2593 0.75 0 0 -3.13353</pose>
        <scale>1.5945 1 1</scale>
        <link name='link'>
          <pose>1.38751 -10.2593 0.75 0 0 -3.13353</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_clone'>
        <pose>-8.6325 -9.41484 0.75 0 -0 0</pose>
        <scale>1 0.702395 1</scale>
        <link name='link'>
          <pose>-8.6325 -9.41484 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_clone'>
        <pose>4.5992 9.15741 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>4.5992 9.15741 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_clone_clone'>
        <pose>-2.59272 -0.967922 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-2.59272 -0.967922 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_clone_clone_0'>
        <pose>4.7058 -4.24546 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>4.7058 -4.24546 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_clone_clone_1_clone'>
        <pose>4.72909 -1.1665 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>4.72909 -1.1665 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_clone_clone_2'>
        <pose>7.1184 -2.76878 0.75 0 -0 0</pose>
        <scale>1 0.865316 1</scale>
        <link name='link'>
          <pose>7.1184 -2.76878 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_5_clone_clone_clone_clone'>
        <pose>-8.86383 -0.367386 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-8.86383 -0.367386 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_7_clone'>
        <pose>-2.08707 2.37686 0.75 0 -0 0</pose>
        <scale>5.14646 1 1</scale>
        <link name='link'>
          <pose>-2.08707 2.37686 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_7_clone_clone'>
        <pose>-11.9133 7.09734 0.75 0 -0 0</pose>
        <scale>0.652993 0.244603 1</scale>
        <link name='link'>
          <pose>-11.9133 7.09734 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_7_clone_clone_1'>
        <pose>-9.08212 -3.08785 0.75 0 -0 0</pose>
        <scale>3.96159 0.707777 1</scale>
        <link name='link'>
          <pose>-9.08212 -3.08785 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_7_clone_clone_2'>
        <pose>-5.82661 -3.21952 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-5.82661 -3.21952 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_7_clone_clone_2_clone'>
        <pose>-6.7523 -3.12345 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-6.7523 -3.12345 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_7_clone_clone_3'>
        <pose>-5.3619 -3.95424 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-5.3619 -3.95424 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_7_clone_clone_4'>
        <pose>-4.89443 -4.91365 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-4.89443 -4.91365 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_7_clone_clone_5'>
        <pose>-4.14032 -5.67151 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-4.14032 -5.67151 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_7_clone_clone_clone_0'>
        <pose>-9.65191 2.07884 0.75 0 -0 0</pose>
        <scale>0.94712 1.82895 1</scale>
        <link name='link'>
          <pose>-9.65191 2.07884 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_9_clone_3'>
        <pose>0.923625 6.78613 0.75 0 -0 0</pose>
        <scale>1 8.21069 1</scale>
        <link name='link'>
          <pose>0.923625 6.78613 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='box_9_clone_clone_9_clone_3_clone_0'>
        <pose>-0.627026 6.01445 0.75 0 -0 0</pose>
        <scale>4.34985 1 1</scale>
        <link name='link'>
          <pose>-0.627026 6.01445 0.75 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>
      <model name='camera'>
        <pose>0 0 0.75 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='camera'>
          <pose>0 0 0.75 0 -0 0</pose>
          <velocity>0 0 -0.049033 0 -0 0</velocity>
          <acceleration>0 0 -9.8066 0 -0 0</acceleration>
          <wrench>0 0 -9.8e-05 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder'>
        <pose>0.985425 -5.52499 0.500002 7e-06 7e-06 -0.000698</pose>
        <scale>2.98284 2.98284 1</scale>
        <link name='link'>
          <pose>0.985425 -5.52499 0.500002 7e-06 7e-06 -0.000698</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>7.96599 -7.96845 1.99693 0.234733 -0.224229 -3.14049</acceleration>
          <wrench>7.96599 -7.96845 1.99693 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder_clone'>
        <pose>-11.0315 2.83002 0.499999 -2e-06 -2e-06 -0.000953</pose>
        <scale>0.209328 0.209328 1</scale>
        <link name='link'>
          <pose>-11.0315 2.83002 0.499999 -2e-06 -2e-06 -0.000953</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-2.41968 1.93899 -1.93925 2.51202 1.3435 -0.232786</acceleration>
          <wrench>-2.41968 1.93899 -1.93925 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder_clone_0'>
        <pose>-12.6691 11.2332 -990448 -1.79717 -1.01253 -2.18176</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-12.6691 11.2332 -990448 -1.79717 -1.01253 -2.18176</pose>
          <velocity>0 0 -4404.47 0 -0 0</velocity>
          <acceleration>0 0 -9.8066 0 -0 0</acceleration>
          <wrench>0 0 -9.8066 0 -0 0</wrench>
        </link>
      </model>
      <model name='unit_cylinder_clone_clone'>
        <pose>-11.0656 6.66284 0.5 -0 -0 -0.001154</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose>-11.0656 6.66284 0.5 -0 -0 -0.001154</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>-1e-06 1e-06 19.6132 -2e-06 -3e-06 0</acceleration>
          <wrench>-1e-06 1e-06 19.6132 0 -0 0</wrench>
        </link>
      </model>
      <light name='sun'>
        <pose>0 0 10 0 -0 0</pose>
      </light>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>4.56771 22.782 51.289 0 1.1298 -1.69937</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
    <model name='box_9_clone_clone_9_clone_3'>
      <pose>0.946415 7.9797 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_8_clone_clone_4_clone_1'>
      <pose>-2.98043 2.34503 1.25 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1.50002 0.1 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1.50002 0.1 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_7_clone'>
      <pose>-5.21616 2.23106 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_7_clone_clone_1'>
      <pose>-6.44194 -2.23118 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_7_clone_clone_2'>
      <pose>-5.82661 -3.0536 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_7_clone_clone_3'>
      <pose>-5.3619 -3.95424 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_7_clone_clone_4'>
      <pose>-4.89443 -5.04481 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_7_clone_clone_5'>
      <pose>-4.42171 -5.82067 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_8_clone_clone_4_clone_2_clone'>
      <pose>-3.89284 -6.16269 1.25 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1.5 0.1 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1.5 0.1 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone'>
      <pose>4.07842 11.7768 1.25 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.49999 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.49999 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_9_clone_3_clone_0'>
      <pose>0.759218 7.54574 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <audio>
      <device>default</device>
    </audio>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2'>
      <pose>4.02856 3.6983 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone_clone'>
      <pose>4.04019 3.74812 1.25 0 -0 1.5473</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone_clone_0'>
      <pose>4.02383 3.01127 1.25 0 -0 1.5473</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_8_clone_clone_4_clone_1_clone'>
      <pose>0.020285 3.21878 1.25 0 -0 0.698352</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1.32602 0.1 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1.32602 0.1 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone'>
      <pose>-8.34973 6.22845 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.517732 6.70249 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.517732 6.70249 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone'>
      <pose>1.47437 -1.14091 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.517734 13.9044 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.517734 13.9044 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_7_clone_clone'>
      <pose>-10.9966 2.18659 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>5.66044 0.999992 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>5.66044 0.999992 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0'>
      <pose>-5.46368 7.21671 1.25 0 0 -2.41059</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 3.73258 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 3.73258 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone'>
      <pose>-4.31601 7.2263 1.25 0 0 -2.41059</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_0'>
      <pose>-3.38949 7.21315 1.25 0 0 -2.41059</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_clone'>
      <pose>-2.35745 7.19861 1.25 0 -0 2.3647</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_1'>
      <pose>-4.37527 8.43199 1.25 0 0 -2.41059</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.60264 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.60264 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder'>
      <pose>5.56711 -4.52536 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone_clone_1'>
      <pose>4.8147 3.74507 1.25 0 -0 1.5473</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone'>
      <pose>9.55648 -4.93176 0.75 0 -0 1.56352</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.517731 3.07803 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.517731 3.07803 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone'>
      <pose>9.54266 -2.76485 0.75 0 -0 1.56352</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.517731 3.07802 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.517731 3.07802 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_1_clone'>
      <pose>8.77437 -7.97204 1.25 0 -0 0.708939</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 4.99875 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 4.99875 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone_clone_clone'>
      <pose>9.14559 0.954223 1.25 0 -0 1.5473</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone_clone_clone_0'>
      <pose>9.15198 1.11685 1.25 0 -0 1.5473</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone_clone_clone_clone'>
      <pose>9.36555 0.57412 1.25 0 -0 1.56028</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_1'>
      <pose>9.23287 2.41719 0.75 0 -0 1.56352</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.517731 4.29141 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.517731 4.29141 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_1_clone'>
      <pose>10.7143 2.50136 0.75 0 -0 -3.13353</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.517731 2.83434 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.517731 2.83434 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_clone_clone'>
      <pose>9.16663 7.48045 1.25 0 -0 2.3647</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_2'>
      <pose>8.12867 7.55946 1.25 0 0 -2.41059</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_0_clone'>
      <pose>10.2349 7.47203 1.25 0 0 -2.41059</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_1_clone_0'>
      <pose>9.37792 1.10044 1.25 0 -0 0.708939</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 4.99876 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 4.99876 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_7_clone_clone_clone_0'>
      <pose>-10.0979 1.89572 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>3.93799 0.466731 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>3.93799 0.466731 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_clone'>
      <pose>-8.6325 -9.22074 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.517731 8.40331 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.517731 8.40331 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone'>
      <pose>-13.2331 -8.119 1.25 0 0 -2.41059</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_clone'>
      <pose>-13.0267 -9.02916 1.25 0 0 -1.91939</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 2.13426 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 2.13426 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone'>
      <pose>1.05576 -11.8471 0.75 0 -0 1.56352</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.517731 9.1748 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.517731 9.1748 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_1_clone_clone'>
      <pose>1.38751 -10.2593 0.75 0 0 -3.13353</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.517731 2.83434 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.517731 2.83434 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_clone'>
      <pose>4.5992 9.85631 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_0'>
      <pose>11.2374 11.5305 1.25 0 0 -2.41059</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.60265 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_0'>
      <pose>9.53139 -9.54857 0.75 0 -0 1.56352</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.843798 3.07714 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.843798 3.07714 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_0_clone'>
      <pose>5.2756 -7.32875 0.75 0 -0 1.56359</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.149813 5.69724 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.149813 5.69724 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_0_clone_clone'>
      <pose>4.58884 -3.70572 0.75 0 -0 -2.33603</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.149813 3.48664 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.149813 3.48664 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_clone_clone'>
      <pose>-2.59272 -0.967922 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_clone_clone_0'>
      <pose>4.44479 -4.24546 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_clone_clone_2'>
      <pose>6.86998 -2.69722 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_0'>
      <pose>-11.4677 -0.34019 0.75 0 -0 1.56359</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.238975 8.51748 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.238975 8.51748 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_clone_clone_clone'>
      <pose>-8.86383 -0.367386 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone_clone_clone_clone_clone_0'>
      <pose>10.3645 -7.27172 1.25 0 -0 3.12409</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 0.695313 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 0.695313 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_clone'>
      <pose>-11.0304 4.51922 0.500019 -9e-06 -9e-06 -0.00062</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>1.49142</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>1.49142</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_clone_clone'>
      <pose>-11.0656 6.6279 0.5 0 0 -0.000643</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.312196</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.312196</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone_clone_clone_clone_clone_0_clone'>
      <pose>8.67421 -8.77015 0.728231 0 -0 3.12409</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 0.695313 1.468</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 0.695313 1.468</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_clone_clone_1_clone'>
      <pose>4.72909 -1.1665 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1.06509 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_clone_0'>
      <pose>-13.5011 11.8236 0.500005 7e-06 7e-06 -0.000629</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>1.49142</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>1.49142</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_clone_0'>
      <pose>-12.6684 9.96416 1.25 0 0 -1.91939</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 2.13425 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 2.13425 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_clone_clone'>
      <pose>-10.5404 11.4399 1.25 0 0 -1.13074</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 2.13426 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 2.13426 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_clone_0_clone_0_clone_2_clone_clone_0_clone'>
      <pose>-10.9454 9.83136 1.25 0.004899 -0 -2.17636</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 2.13425 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 2.13425 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_6_clone_clone_4_clone_clone_1_clone'>
      <pose>-8.92093 -3.32073 1.25 0 0 -0.001309</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.1 1.5 2.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_5_clone_2_clone_0_clone_2_clone_0_clone_clone_clone_clone_0_clone'>
      <pose>-13.5014 -9.06923 0.75 0 -0 1.56352</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.843798 3.07714 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.843798 3.07714 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='box_9_clone_clone_7_clone_clone_2_clone'>
      <pose>-6.7523 -3.12345 0.75 0 -0 0</pose>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1.5</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/White</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='camera'>
      <link name='camera'>
        <inertial>
          <pose>0 0 0 0 -0 0</pose>
          <mass>1e-05</mass>
          <inertia>
            <ixx>1e-06</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1e-06</iyy>
            <iyz>0</iyz>
            <izz>1e-06</izz>
          </inertia>
        </inertial>
        <collision name='camera_collision'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.001 0.001 0.001</size>
            </box>
          </geometry>
          <surface>
            <contact>
              <ode/>
            </contact>
            <friction>
              <ode/>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='camera_visual'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.001 0.001 0.001</size>
            </box>
          </geometry>
        </visual>
        <sensor name='camera' type='camera'>
          <update_rate>15</update_rate>
          <camera name='head'>
            <horizontal_fov>2</horizontal_fov>
            <image>
              <width>320</width>
              <height>180</height>
              <format>R8G8B8</format>
            </image>
            <clip>
              <near>0.1</near>
              <far>200</far>
            </clip>
          </camera>
          <plugin name='camera_controller' filename='libgazebo_ros_camera.so'>
            <alwaysOn>1</alwaysOn>
            <updateRate>0.0</updateRate>
            <cameraName>camera</cameraName>
            <imageTopicName>image</imageTopicName>
            <cameraInfoTopicName>camera_info</cameraInfoTopicName>
            <frameName>camera</frameName>
            <hackBaseline>0.0</hackBaseline>
            <distortionK1>0.0</distortionK1>
            <distortionK2>0.0</distortionK2>
            <distortionK3>0.0</distortionK3>
            <distortionT1>0.0</distortionT1>
            <distortionT2>0.0</distortionT2>
            <robotNamespace>/</robotNamespace>
          </plugin>
        </sensor>
        <self_collide>0</self_collide>
        <enable_wind>0</enable_wind>
        <kinematic>0</kinematic>
      </link>
      <pose>0 0 0 0 -0 0</pose>
    </model>
  </world>
</sdf>
