<launch>
    <node pkg="octomap_server" type="octomap_server_node" name="octomap_server"> 
        <!-- Grid resolution -->
        <param name="resolution" value="0.1" />
        
        <!-- World frame -->
        <param name="frame_id" type="string" value="map" />
    
        <param name="sensor_model/max_range" value="10.0" />
        <param name="latch" value="true" />
    
        <!-- Range of point cloud --> 
        <param name="pointcloud_max_z" value="0.8" />
        <param name="pointcloud_min_z" value="0.1" />
    
        <param name="graound_filter_angle" value="3.14" />
        
        <remap from="cloud_in" to="sensor_scan" />
        <remap from="projected_map" to="map" />
    </node>
     
    </launch>
