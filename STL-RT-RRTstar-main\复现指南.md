# STL-RT-RRT*完整复现指南

## 项目概述

STL-RT-RRT*是一个将信号时序逻辑(STL)约束集成到实时RRT*算法中的社交机器人导航系统。它能够在动态环境中为机器人规划满足社交规范的路径。

## 环境配置

### 1. 依赖安装

```bash
pip install numpy matplotlib scipy dill more-itertools
```

### 2. 项目结构

```
STL-RT-RRTstar-main/
├── STLFormula.py           # STL公式定义和鲁棒性计算
├── rt_rrt_star.py          # 基础实时RRT*算法
├── stl_rt_rrt_star.py      # 集成STL约束的RRT*算法
├── ellipsis.py             # 椭圆采样优化
├── simple_demo.py          # 简化演示脚本
├── stl_tasks.py            # STL任务管理器
├── 复现指南.md             # 本文档
├── stl_specifications/     # 预计算的树文件
└── img/                    # 结果图片保存目录
```

## 当前STL任务分析

### 主要任务类型

1. **房间参考系任务**：基于全局坐标系的约束
   - 访问特定区域
   - 避开危险区域
   - 顺序访问多个区域

2. **人类参考系任务**：基于人类位置的相对约束（社交导航）
   - **hurry（急匆匆）**：快速从人类旁边通过
   - **walk（正常行走）**：以正常速度通过
   - **fragile（小心翼翼）**：给人类更大空间，小心通过

### 当前激活的任务

项目当前使用的是**fragile（小心翼翼）**任务：

```python
# 定义左右两侧通过区域
fragile_left = STLPredicate2D(0, 1, -95, -80, -150, -45)
fragile_right = STLPredicate2D(0, 1, 80, 95, -60, 50)
fragile = Disjunction([fragile_left, fragile_right])
specification = Untimed_Eventually(fragile)
```

**任务含义**：
- 机器人最终必须从人类的左侧或右侧通过
- 左侧区域：x∈[-95,-80], y∈[-150,-45]
- 右侧区域：x∈[80,95], y∈[-60,50]
- 这给人类留出了最大的安全空间

### 社交行为对比

| 行为类型 | 左侧区域 | 右侧区域 | 特点 |
|---------|---------|---------|------|
| hurry | [-80,-70]×[-100,0] | [60,75]×[-60,50] | 快速通过，距离较近 |
| walk | [-90,-80]×[-90,0] | [70,85]×[-60,50] | 正常通过，中等距离 |
| fragile | [-95,-80]×[-150,-45] | [80,95]×[-60,50] | 小心通过，距离最远 |

## 完整复现步骤

### 步骤1：基础功能测试

```bash
python simple_demo.py
```

这将演示：
- STL公式的创建和鲁棒性计算
- 当前项目任务的详细分析
- 如何修改和创建自定义任务

### 步骤2：运行完整算法

```bash
python stl_rt_rrt_star.py
```

这将：
- 加载预计算的RRT*树
- 执行实时路径规划
- 在动态环境中避开移动的人类
- 生成满足STL约束的路径
- 保存结果图片到img/目录

### 步骤3：查看结果

检查img/目录中生成的图片：
- `0.0_stl.png`, `0.5_stl.png`, ... : 不同时间步的规划结果
- 红色虚线：规划的路径
- 蓝色点：RRT*树的节点
- 圆形障碍物：动态移动的人类

## 如何修改STL任务

### 1. 修改区域参数

```python
# 原始右侧通过区域
fragile_right = STLPredicate2D(0, 1, 80, 95, -60, 50)

# 修改为更宽松的区域
wider_right = STLPredicate2D(0, 1, 75, 100, -70, 60)

# 修改为更严格的区域  
narrower_right = STLPredicate2D(0, 1, 85, 90, -50, 40)
```

### 2. 修改时间约束

```python
# 无时间限制
untimed_task = Untimed_Eventually(zone)

# 添加时间窗口
timed_task = Eventually(zone, 30, 40)  # 必须在30-40时间步内满足

# 持续约束
always_task = Always(zone, 0, 50)     # 在0-50时间步内始终满足
```

### 3. 创建复合任务

```python
# 组合多个约束
right_pass = STLPredicate2D(0, 1, 80, 95, -60, 50)
avoid_danger = Negation(STLPredicate2D(0, 1, -20, 20, -20, 20))

compound_task = Conjunction([
    Eventually(right_pass, 30, 40),    # 在30-40步内从右侧通过
    Always(avoid_danger, 0, 50)        # 始终避开危险区域
])
```

### 4. 在代码中应用新任务

在`stl_rt_rrt_star.py`的main函数中找到这一行：

```python
specification = untimed_specification_fragile
```

替换为您的新任务：

```python
# 使用您自定义的任务
specification = your_custom_task
```

## 自定义任务示例

### 1. 礼让行为

```python
# 机器人先让人类通过，然后自己通过
central_channel = STLPredicate2D(0, 1, -20, 20, -50, 50)
wait_phase = Always(Negation(central_channel), 0, 30)  # 前30步避开
pass_phase = Eventually(central_channel, 35, 45)       # 35-45步通过
polite_task = Conjunction([wait_phase, pass_phase])
```

### 2. 跟随行为

```python
# 机器人保持在人类后方
follow_zone = STLPredicate2D(0, 1, -60, -40, -30, 30)
follow_task = Always(follow_zone, 0, 100)
```

### 3. 引导行为

```python
# 机器人先到达目标位置引导人类
lead_zone = STLPredicate2D(0, 1, 40, 60, -20, 20)
lead_task = Eventually(lead_zone, 10, 20)
```

## 技术原理要点

### 1. STL鲁棒性
- 正值：满足约束，数值越大满足程度越高
- 负值：违反约束，绝对值越大违反程度越严重
- 零值：刚好在约束边界上

### 2. 成本函数
- 总成本 = 路径长度成本 + 障碍物成本 + STL成本
- STL成本通过鲁棒性的负值积分计算
- 使用梯形积分法进行增量计算

### 3. 实时重规划
- 动态更新树的根节点
- 重新计算所有节点的STL成本
- 执行有限次数的树扩展和重连

## 常见问题解决

### 1. NumPy版本兼容性问题
如果遇到matplotlib导入错误，可能需要降级NumPy：
```bash
pip install "numpy<2.0"
```

### 2. 缺少预计算树文件
如果没有预计算的树文件，算法会自动创建新树，但需要更长时间。

### 3. 图片保存失败
确保img/目录存在：
```bash
mkdir img
```

## 扩展建议

1. **添加新的社交行为**：定义新的STL约束区域
2. **多人场景**：扩展为多个动态障碍物
3. **复杂环境**：添加静态障碍物和复杂地图
4. **实时性能优化**：调整算法参数以平衡质量和速度

## 总结

STL-RT-RRT*提供了一个强大的框架，将形式化的社交规范集成到路径规划中。通过修改STL约束，您可以轻松定义各种社交导航行为，从急匆匆的快速通过到小心翼翼的礼让行为。
