#!/usr/bin/env python3
"""
STL-RT-RRT*可视化和分析工具
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from STLFormula import *

def visualize_stl_zones(task_name, specification, save_path=None):
    """可视化STL约束区域"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 左图：房间参考系
    ax1.set_xlim(0, 520)
    ax1.set_ylim(0, 440)
    ax1.set_title(f'房间参考系 - {task_name}')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlabel('X (cm)')
    ax1.set_ylabel('Y (cm)')
    
    # 添加起点和终点
    ax1.plot(50, 50, 'go', markersize=10, label='起点')
    ax1.plot(470, 390, 'ro', markersize=10, label='终点')
    
    # 右图：人类参考系
    ax2.set_xlim(-200, 200)
    ax2.set_ylim(-200, 200)
    ax2.set_title(f'人类参考系 - {task_name}')
    ax2.grid(True, alpha=0.3)
    ax2.set_xlabel('X (相对于人类, cm)')
    ax2.set_ylabel('Y (相对于人类, cm)')
    
    # 添加人类位置
    ax2.plot(0, 0, 'bo', markersize=12, label='人类')
    
    # 根据任务类型绘制约束区域
    if 'visit_zone' in task_name or 'zone' in task_name:
        # 房间参考系区域
        rect = patches.Rectangle((150, 300), 50, 50, 
                               linewidth=2, edgecolor='green', 
                               facecolor='lightgreen', alpha=0.5)
        ax1.add_patch(rect)
        ax1.text(175, 325, '目标区域', ha='center', va='center')
        
    elif 'danger' in task_name:
        # 危险区域
        rect = patches.Rectangle((200, 200), 100, 100, 
                               linewidth=2, edgecolor='red', 
                               facecolor='lightcoral', alpha=0.5)
        ax1.add_patch(rect)
        ax1.text(250, 250, '危险区域', ha='center', va='center')
        
    elif 'hurry' in task_name:
        # 急匆匆通过区域
        rect1 = patches.Rectangle((-80, -100), 10, 100, 
                                linewidth=2, edgecolor='orange', 
                                facecolor='moccasin', alpha=0.7)
        rect2 = patches.Rectangle((60, -60), 15, 110, 
                                linewidth=2, edgecolor='orange', 
                                facecolor='moccasin', alpha=0.7)
        ax2.add_patch(rect1)
        ax2.add_patch(rect2)
        ax2.text(-75, -50, '左侧\n快速通过', ha='center', va='center')
        ax2.text(67.5, -5, '右侧\n快速通过', ha='center', va='center')
        
    elif 'walk' in task_name or 'normal' in task_name:
        # 正常行走区域
        rect1 = patches.Rectangle((-90, -90), 10, 90, 
                                linewidth=2, edgecolor='blue', 
                                facecolor='lightblue', alpha=0.7)
        rect2 = patches.Rectangle((70, -60), 15, 110, 
                                linewidth=2, edgecolor='blue', 
                                facecolor='lightblue', alpha=0.7)
        ax2.add_patch(rect1)
        ax2.add_patch(rect2)
        ax2.text(-85, -45, '左侧\n正常通过', ha='center', va='center')
        ax2.text(77.5, -5, '右侧\n正常通过', ha='center', va='center')
        
    elif 'fragile' in task_name or 'careful' in task_name:
        # 小心翼翼区域
        rect1 = patches.Rectangle((-95, -150), 15, 105, 
                                linewidth=2, edgecolor='purple', 
                                facecolor='plum', alpha=0.7)
        rect2 = patches.Rectangle((80, -60), 15, 110, 
                                linewidth=2, edgecolor='purple', 
                                facecolor='plum', alpha=0.7)
        ax2.add_patch(rect1)
        ax2.add_patch(rect2)
        ax2.text(-87.5, -97.5, '左侧\n小心通过', ha='center', va='center')
        ax2.text(87.5, -5, '右侧\n小心通过', ha='center', va='center')
        
    elif 'distance' in task_name:
        # 安全距离区域
        circle = patches.Circle((0, 0), 50, 
                              linewidth=2, edgecolor='red', 
                              facecolor='lightcoral', alpha=0.5)
        ax2.add_patch(circle)
        ax2.text(0, -70, '禁止进入\n安全距离', ha='center', va='center')
    
    ax1.legend()
    ax2.legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"可视化图片已保存到: {save_path}")
    else:
        plt.show()
    
    return fig

def plot_robustness_over_time(trajectory, specification, title="STL鲁棒性随时间变化"):
    """绘制STL鲁棒性随时间的变化"""
    
    robustness_values = []
    time_steps = range(len(trajectory))
    
    for t in time_steps:
        try:
            # 计算当前时间步的鲁棒性
            rho = specification.robustness(trajectory, t)
            robustness_values.append(rho)
        except:
            robustness_values.append(0)
    
    plt.figure(figsize=(12, 6))
    plt.plot(time_steps, robustness_values, 'b-', linewidth=2, label='鲁棒性值')
    plt.axhline(y=0, color='r', linestyle='--', alpha=0.7, label='满足/违反边界')
    plt.fill_between(time_steps, robustness_values, 0, 
                     where=[r >= 0 for r in robustness_values], 
                     color='green', alpha=0.3, label='满足约束')
    plt.fill_between(time_steps, robustness_values, 0, 
                     where=[r < 0 for r in robustness_values], 
                     color='red', alpha=0.3, label='违反约束')
    
    plt.xlabel('时间步')
    plt.ylabel('鲁棒性值')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    return plt.gcf()

def compare_specifications():
    """比较不同STL规范的约束区域"""
    
    from stl_tasks import STLTaskManager
    manager = STLTaskManager()
    
    # 选择几个代表性任务进行比较
    tasks_to_compare = ['hurry_pass', 'normal_walk', 'careful_pass']
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    colors = ['orange', 'blue', 'purple']
    alphas = [0.7, 0.7, 0.7]
    
    for i, task_name in enumerate(tasks_to_compare):
        ax = axes[i]
        task = manager.get_task(task_name)
        
        ax.set_xlim(-200, 200)
        ax.set_ylim(-200, 200)
        ax.set_title(f'{task["description"]}')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('X (相对于人类, cm)')
        ax.set_ylabel('Y (相对于人类, cm)')
        
        # 添加人类位置
        ax.plot(0, 0, 'bo', markersize=12, label='人类')
        
        # 根据任务绘制约束区域
        if 'hurry' in task_name:
            rect1 = patches.Rectangle((-80, -100), 10, 100, 
                                    linewidth=2, edgecolor=colors[i], 
                                    facecolor=colors[i], alpha=alphas[i])
            rect2 = patches.Rectangle((60, -60), 15, 110, 
                                    linewidth=2, edgecolor=colors[i], 
                                    facecolor=colors[i], alpha=alphas[i])
            ax.add_patch(rect1)
            ax.add_patch(rect2)
            
        elif 'normal' in task_name:
            rect1 = patches.Rectangle((-90, -90), 10, 90, 
                                    linewidth=2, edgecolor=colors[i], 
                                    facecolor=colors[i], alpha=alphas[i])
            rect2 = patches.Rectangle((70, -60), 15, 110, 
                                    linewidth=2, edgecolor=colors[i], 
                                    facecolor=colors[i], alpha=alphas[i])
            ax.add_patch(rect1)
            ax.add_patch(rect2)
            
        elif 'careful' in task_name:
            rect1 = patches.Rectangle((-95, -150), 15, 105, 
                                    linewidth=2, edgecolor=colors[i], 
                                    facecolor=colors[i], alpha=alphas[i])
            rect2 = patches.Rectangle((80, -60), 15, 110, 
                                    linewidth=2, edgecolor=colors[i], 
                                    facecolor=colors[i], alpha=alphas[i])
            ax.add_patch(rect1)
            ax.add_patch(rect2)
        
        ax.legend()
    
    plt.tight_layout()
    plt.savefig('img/stl_specifications_comparison.png', dpi=150, bbox_inches='tight')
    print("STL规范比较图已保存到: img/stl_specifications_comparison.png")
    
    return fig

if __name__ == '__main__':
    # 生成所有任务的可视化
    from stl_tasks import STLTaskManager
    
    manager = STLTaskManager()
    
    # 为每个任务生成可视化
    for task_name in ['hurry_pass', 'normal_walk', 'careful_pass']:
        task = manager.get_task(task_name)
        visualize_stl_zones(task_name, task['specification'], 
                          f'img/stl_zones_{task_name}.png')
    
    # 生成比较图
    compare_specifications()
    
    print("所有可视化图片已生成完成！")
