以下是翻译内容：

---

# PNGNav

这是为 TurtleBot 导航实现的 ROS 版本的 NIRRT*-PNG（带有点云网络引导的神经信息 RRT*），即我们在 2024 年 ICRA 论文中的方法。

**神经信息 RRT*\***：在可接受的椭球约束下，基于点云状态表示的学习型路径规划

作者：黄哲（Zhe Huang）、陈洪宇（<PERSON><PERSON> Chen）、约翰·波霍维（<PERSON>ey）、凯瑟琳·德里格斯-坎贝尔（Katherine Driggs-Campbell）

[论文] [arXiv] [主 GitHub 仓库] [机器人演示 GitHub 仓库] [项目 Google 网站] [YouTube 演示] [YouTube 机器人演示]

所有代码均在 Ubuntu 20.04 系统上开发和测试，使用 CUDA 12.0、ROS Noetic、conda 23.11.0、Python 3.9.0 和 PyTorch 2.0.1。此仓库提供了 ROS 包 png_navigation，其中包含 RRT*、信息 RRT*、神经 RRT* 以及我们的 NIRRT*-PNG 的 rospy 实现，用于 TurtleBot 导航。我们提供了如何在 Gazebo 模拟环境中使用 png_navigation 的说明，png_navigation 也可以轻松应用于现实场景。

## 引用

如果此仓库对你有帮助，请引用以下文献：

```
@inproceedings{huang2024neural,
  title={Neural Informed RRT*: Learning-based Path Planning with Point Cloud State Representations under Admissible Ellipsoidal Constraints},
  author={Huang, Zhe and Chen, Hongyu and Pohovey, John and Driggs-Campbell, Katherine},
  booktitle={2024 IEEE International Conference on Robotics and Automation (ICRA)},
  pages={8742--8748},
  year={2024},
  organization={IEEE}
}
```

## 安装

运行以下命令：

```bash
cd ~/PNGNav
catkin_make
```

运行以下命令：

```bash
conda env create -f environment.yml
```

将脚本中的 shebang 行中的 `/home/<USER>/miniconda3/` 替换为你的系统路径。例如，如果你使用的是 Ubuntu 和 miniconda3，且你的用户名为 abc，则将 `/home/<USER>/miniconda3/` 替换为 `/home/<USER>/miniconda3/`。

下载 PNG 的 PointNet++ 模型权重，创建文件夹 `model_weights/`，路径为 `PNGNav/src/png_navigation/src/png_navigation/wrapper/pointnet_pointnet2/`，并将下载的 `pointnet2_sem_seg_msg_pathplan.pth` 移动到 `model_weights` 文件夹中。

```bash
cd ~/PNGNav/src/png_navigation/src/png_navigation/wrapper/pointnet_pointnet2/
mkdir model_weights
cd model_weights
mv ~/Downloads/pointnet2_sem_seg_msg_pathplan.pth .
```

如果你有 `map_realworld.pgm` 和 `map_realworld.yaml` 文件，将它们移动到 `PNGNav/src/png_navigation/src/png_navigation/maps` 文件夹中。

## 如何运行 TurtleBot3 Gazebo 模拟

### 模拟设置

按照 TurtleBot3 官方网站上的教程安装依赖项，并测试 TurtleBot3 Gazebo 模拟。

```bash
sudo apt-get install ros-noetic-joy ros-noetic-teleop-twist-joy \
  ros-noetic-teleop-twist-keyboard ros-noetic-laser-proc \
  ros-noetic-rgbd-launch ros-noetic-rosserial-arduino \
  ros-noetic-rosserial-python ros-noetic-rosserial-client \
  ros-noetic-rosserial-msgs ros-noetic-amcl ros-noetic-map-server \
  ros-noetic-move-base ros-noetic-urdf ros-noetic-xacro \
  ros-noetic-compressed-image-transport ros-noetic-rqt* ros-noetic-rviz \
  ros-noetic-gmapping ros-noetic-navigation ros-noetic-interactive-markers
sudo apt install ros-noetic-dynamixel-sdk
sudo apt install ros-noetic-turtlebot3-msgs
sudo apt install ros-noetic-turtlebot3
cd ~/PNGNav/src
git clone -b noetic-devel https://github.com/ROBOTIS-GIT/turtlebot3_simulations.git
cd ~/PNGNav && catkin_make
```

### 指令

将以下行添加到 `~/.bashrc` 文件中：

```bash
export TURTLEBOT3_MODEL=waffle_pi
```

启动 Gazebo 模拟。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
roslaunch turtlebot3_gazebo turtlebot3_world.launch
```

为 Turtlebot3 启动地图服务器和 AMCL。请注意，这是我们的 png_navigation 包中的启动文件，不包括 move_base 和 rviz 的启动。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
roslaunch png_navigation turtlebot3_navigation.launch
```

启动 rviz。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
roslaunch png_navigation rviz_navigation_static.launch
```

通过遥操作估计 Turtlebot3 的姿态。姿态估计完成后，记得终止 `turtlebot3_teleop_key.launch`。

```bash
conda deactivate
roslaunch turtlebot3_teleop turtlebot3_teleop_key.launch
```

启动规划算法。当你看到 “Global Planner is initialized.” 时，你可以在 rviz 中选择导航目标开始规划。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
roslaunch png_navigation nirrt_star_c.launch
```

或者运行以下任意一条命令：

```bash
roslaunch png_navigation nirrt_star.launch
roslaunch png_navigation nrrt_star.launch
roslaunch png_navigation irrt_star.launch
roslaunch png_navigation rrt_star.launch
```

## 如何运行动态障碍物实现

以下是运行模拟中动态障碍物（我们将其称为移动人类）实现的指令。

将以下行添加到 `~/.bashrc` 文件中：

```bash
export TURTLEBOT3_MODEL=waffle_pi
```

启动 Gazebo 模拟。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
roslaunch turtlebot3_gazebo turtlebot3_world.launch
```

为 Turtlebot3 启动地图服务器和 AMCL。请注意，这是我们的 png_navigation 包中的启动文件，不包括 move_base 和 rviz 的启动。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
roslaunch png_navigation turtlebot3_navigation.launch
```

启动 rviz。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
roslaunch png_navigation rviz_navigation_static.launch
```

通过遥操作估计 Turtlebot3 的姿态。姿态估计完成后，记得终止 `turtlebot3_teleop_key.launch`。

```bash
conda deactivate
roslaunch turtlebot3_teleop turtlebot3_teleop_key.launch
```

创建移动人类。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
rosrun png_navigation moving_humans_with_noisy_measurements.py
```

将 `/dr_spaam_detections/PoseArray` 和 `/gt_human_positions/PoseArray` 添加到 rviz 中。

启动人类检测器。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
rosrun png_navigation human_checker_gazebo.py
```

启动动态障碍物感知规划算法。

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
roslaunch png_navigation nrrt_star_dynamic_obstacles.launch
```

或者运行以下命令：

```bash
cd ~/PNGNav
conda deactivate
source devel/setup.bash
roslaunch png_navigation nirrt_star_c_dynamic_obstacles.launch
```

### 注意事项

- 通过修改 `PNGNav/src/png_navigation/scripts_dynamic_obstacles/moving_humans_with_noisy_measurements.py` 中的 `vx` 和 `vy` 来改变移动人类的速度。
- 通过修改 `PNGNav/src/png_navigation/scripts_dynamic_obstacles/human_checker_gazebo.py` 中的 `human_detection_radius` 来改变机器人对人类的检测半径。

## 在 TurtleBot 2i 上进行现实世界部署

我们使用 CrowdNav_Sim2Real_Turtlebot 将 png_navigation 中的规划算法部署到 TurtleBot 2i 上。此外，记得在本仓库的 `local_planner_node.py` 和 `local_planner_node_check.py` 中注释掉带有 `# gazebo` 的行，并取消注释带有 `# real world` 的行，以进行现实世界部署。

```python
self.cmd_vel = rospy.Publisher('cmd_vel', Twist, queue_size=5) # gazebo
# * self.cmd_vel = rospy.Publisher('cmd_vel_mux/input/teleop', Twist, queue_size=5) # real world
```

## 如何创建自己的地图 YAML 文件

完成 SLAM 并将地图保存为 `.pgm` 文件后，你还会得到一个 YAML 文件。编辑该文件，使其看起来如下：

```yaml
image: /home/<USER>/map_gazebo.pgm
resolution: 0.010000
origin: [-10.000000, -10.000000, 0.000000]
negate: 0
occupied_thresh: 0.65
free_thresh: 0.196
setup: 'world'
free_range: [-2, -2, 2, 2]
circle_obstacles: [[1.1, 1.1, 0.15],
                   [1.1, 0, 0.15],
                   [1.1, -1.1, 0.15],
                   [0, 1.1, 0.15],
                   [0, 0, 0.15],
                   [0, -1.1, 0.15],
                   [-1.1, 1.1, 0.15],
                   [-1.1, 0, 0.15],
                   [-1.1, -1.1, 0.15]]
rectangle_obstacles: []
```

字段格式如下：

- `free_range_pixel`: `[xmin, ymin, xmax, ymax]`
- `circle_obstacles`: `[[x_center_1, y_center_1, r_1], [x_center_2, y_center_2, r_2], [x_center_3, y_center_3, r_3], ..., [x_center_n, y_center_n, r_n]]`
- `rectangle_obstacles`: `[[xmin_1, ymin_1, xmax_1, ymax_1], [xmin_2, ymin_2, xmax_2, ymax_2], [xmin_3, ymin_3, xmax_3, ymax_3], ..., [xmin_n, ymin_n, xmax_n, ymax_n]]`

将 `.pgm` 文件和编辑后的 `.yaml` 文件移动到 `PNGNav/src/png_navigation/src/png_navigation/maps` 文件夹中。保持它们的名称一致，例如 `abc.pgm` 和 `abc.yaml`。运行启动文件时，运行以下命令：

```bash
roslaunch png_navigation nirrt_star_c.launch map:=abc
```

你可以保留其他字段并保留它们。以下是这些字段的含义参考。我们使用 ros_map_editor 定位障碍物关键点的像素，以定义几何形状，然后将像素坐标转换为世界位置。如果你也需要从像素地图转换以获取现实世界中的几何配置，可以使用 `PNGNav/src/png_navigation/src/png_navigation/maps/map_utils.py` 中的 `get_transform_pixel_to_world` 和 `pixel_to_world_coordinates` 函数。

**必需字段：**

- `image`：包含占用数据的图像文件路径；可以是绝对路径，也可以是相对于 YAML 文件位置的相对路径。
- `resolution`：地图的分辨率，单位为米/像素。
- `origin`：地图中左下角像素的二维位置，表示为 (x, y, yaw)，其中 yaw 为逆时针旋转角度（yaw=0 表示无旋转）。系统当前的许多部分忽略 yaw。
- `occupied_thresh`：占用概率大于此阈值的像素被视为完全占用。
- `free_thresh`：占用概率小于此阈值的像素被视为完全空闲。
- `negate`：是否反转黑白空闲/占用语义（阈值的解释不受影响）。

## 如何移植到你的移动机器人

- 修改 `PNGNav/src/png_navigation/src/png_navigation/configs/rrt_star_config.py` 中的 `robot_config`。目前，只有 `robot_config.clearance_radius` 对全局规划有影响。
- 修改 `src/png_navigation/scripts/local_planner_node.py`。特别是确保类 `LocalPlanner` 中的 `self.cmd_vel`、`self.odom_frame` 和 `self.base_frame` 与你的机器人设置匹配。根据需要调整参数。

---

希望以上翻译对你有帮助！