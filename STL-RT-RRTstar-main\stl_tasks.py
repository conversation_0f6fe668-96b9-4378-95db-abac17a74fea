#!/usr/bin/env python3
"""
STL任务定义和配置文件
包含各种预定义的STL任务，方便修改和扩展
"""

from STLFormula import *

class STLTaskManager:
    """STL任务管理器"""
    
    def __init__(self):
        self.tasks = {}
        self._create_predefined_tasks()
    
    def _create_predefined_tasks(self):
        """创建预定义的STL任务"""
        
        # ========== 房间参考系任务 ==========
        
        # 任务1: 机器人必须经过特定区域
        zone1 = STLPredicate2D(0, 1, 150, 200, 300, 350)
        self.tasks['visit_zone'] = {
            'specification': Eventually(zone1, 20, 40),
            'description': '机器人必须在20-40时间步内访问区域(150-200, 300-350)',
            'type': 'room_referential'
        }
        
        # 任务2: 机器人必须避开危险区域
        danger_zone = STLPredicate2D(0, 1, 200, 300, 200, 300)
        self.tasks['avoid_danger'] = {
            'specification': Always(Negation(danger_zone), 0, 50),
            'description': '机器人必须始终避开危险区域(200-300, 200-300)',
            'type': 'room_referential'
        }
        
        # 任务3: 复合任务 - 先访问A区域，再访问B区域
        zone_a = STLPredicate2D(0, 1, 100, 150, 100, 150)
        zone_b = STLPredicate2D(0, 1, 350, 400, 350, 400)
        visit_a = Eventually(zone_a, 10, 25)
        visit_b = Eventually(zone_b, 30, 50)
        self.tasks['sequential_visit'] = {
            'specification': Conjunction([visit_a, visit_b]),
            'description': '机器人先访问区域A(100-150, 100-150)，再访问区域B(350-400, 350-400)',
            'type': 'room_referential'
        }
        
        # ========== 人类参考系任务（社交导航）==========
        
        # 任务4: 急匆匆通过（hurry）
        hurry_left = STLPredicate2D(0, 1, -80, -70, -100, 0)
        hurry_right = STLPredicate2D(0, 1, 60, 75, -60, 50)
        hurry_zones = Disjunction([hurry_left, hurry_right])
        self.tasks['hurry_pass'] = {
            'specification': Eventually(hurry_zones, 25, 35),
            'description': '机器人应该快速从人类左侧或右侧通过',
            'type': 'human_referential'
        }
        
        # 任务5: 正常行走通过（walk）
        walk_left = STLPredicate2D(0, 1, -90, -80, -90, 0)
        walk_right = STLPredicate2D(0, 1, 70, 85, -60, 50)
        walk_zones = Disjunction([walk_left, walk_right])
        self.tasks['normal_walk'] = {
            'specification': Eventually(walk_zones, 30, 40),
            'description': '机器人以正常速度从人类旁边通过',
            'type': 'human_referential'
        }
        
        # 任务6: 小心翼翼通过（fragile）
        fragile_left = STLPredicate2D(0, 1, -95, -80, -150, -45)
        fragile_right = STLPredicate2D(0, 1, 80, 95, -60, 50)
        fragile_zones = Disjunction([fragile_left, fragile_right])
        self.tasks['careful_pass'] = {
            'specification': Untimed_Eventually(fragile_zones),
            'description': '机器人小心翼翼地从人类旁边通过，给予更大空间',
            'type': 'human_referential'
        }
        
        # 任务7: 保持距离
        safe_distance = STLPredicate2D(0, 1, -50, 50, -50, 50)
        self.tasks['keep_distance'] = {
            'specification': Always(Negation(safe_distance), 0, 100),
            'description': '机器人始终与人类保持安全距离',
            'type': 'human_referential'
        }
        
        # ========== 自定义任务示例 ==========
        
        # 任务8: 礼让行为 - 先让人类通过，再自己通过
        blocking_zone = STLPredicate2D(0, 1, -30, 30, -30, 30)
        wait_phase = Always(Negation(blocking_zone), 0, 20)  # 前20步避开中央区域
        pass_phase = Eventually(blocking_zone, 25, 40)       # 20步后通过中央区域
        self.tasks['polite_behavior'] = {
            'specification': Conjunction([wait_phase, pass_phase]),
            'description': '机器人先礼让人类通过，然后自己通过',
            'type': 'human_referential'
        }
    
    def get_task(self, task_name):
        """获取指定任务"""
        if task_name not in self.tasks:
            raise ValueError(f"未知任务: {task_name}. 可用任务: {list(self.tasks.keys())}")
        return self.tasks[task_name]
    
    def list_tasks(self):
        """列出所有可用任务"""
        print("可用的STL任务:")
        print("=" * 50)
        for name, task in self.tasks.items():
            print(f"任务名: {name}")
            print(f"类型: {task['type']}")
            print(f"描述: {task['description']}")
            print("-" * 30)
    
    def add_custom_task(self, name, specification, description, task_type='custom'):
        """添加自定义任务"""
        self.tasks[name] = {
            'specification': specification,
            'description': description,
            'type': task_type
        }
        print(f"已添加自定义任务: {name}")

# 使用示例
if __name__ == '__main__':
    manager = STLTaskManager()
    manager.list_tasks()
    
    # 获取特定任务
    task = manager.get_task('careful_pass')
    print(f"\n选中任务: {task['description']}")
    print(f"STL规范: {task['specification']}")
